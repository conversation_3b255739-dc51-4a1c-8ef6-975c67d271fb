package ${packageName}.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

#foreach ($import in $importList)
import ${import};
#end
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName}业务对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="BaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("${functionName}业务对象")
public class ${ClassName}Bo extends ${Entity} {

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField) && ($column.query || $column.isInsert || $column.isEdit))
    /**
     * $column.columnComment
     */
#if($column.isInsert && $column.isEdit)
#set($Group="AddGroup.class, EditGroup.class")
#elseif($column.isInsert)
#set($Group="AddGroup.class")
#elseif($column.isEdit)
#set($Group="EditGroup.class")
#end
#if($column.isRequired == 1)
    @ApiModelProperty(value = "$column.columnComment", required = true)
#if($column.javaType == 'String')
    @NotBlank(message = "$column.columnComment不能为空", groups = { $Group })
#else
    @NotNull(message = "$column.columnComment不能为空", groups = { $Group })
#end
#else
    @ApiModelProperty(value = "$column.columnComment")
#end
    private $column.javaType $column.javaField;

#end
#end

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
