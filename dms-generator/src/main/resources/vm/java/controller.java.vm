package ${packageName}.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import ${packageName}.domain.vo.${ClassName}Vo;
import ${packageName}.domain.bo.${ClassName}Bo;
import ${packageName}.service.I${ClassName}Service;
#if($table.crud || $table.sub)
import com.rzdata.framework.core.page.TableDataInfo;
#elseif($table.tree)
#end
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Validated
@Api(value = "${functionName}控制器", tags = {"${functionName}管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController {

    private final I${ClassName}Service i${ClassName}Service;

    /**
     * 查询${functionName}列表
     */
    @ApiOperation("查询${functionName}列表")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
    @GetMapping("/list")
#if($table.crud || $table.sub)
    public TableDataInfo<${ClassName}Vo> list(@Validated(QueryGroup.class) ${ClassName}Bo bo) {
        return i${ClassName}Service.queryPageList(bo);
    }
#elseif($table.tree)
    public AjaxResult<List<${ClassName}Vo>> list(@Validated(QueryGroup.class) ${ClassName}Bo bo) {
        List<${ClassName}Vo> list = i${ClassName}Service.queryList(bo);
        return AjaxResult.success(list);
    }
#end

    /**
     * 导出${functionName}列表
     */
    @ApiOperation("导出${functionName}列表")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ${ClassName}Bo bo, HttpServletResponse response) {
        List<${ClassName}Vo> list = i${ClassName}Service.queryList(bo);
        ExcelUtil.exportExcel(list, "${functionName}", ${ClassName}Vo.class, response);
    }

    /**
     * 获取${functionName}详细信息
     */
    @ApiOperation("获取${functionName}详细信息")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
    @GetMapping("/{${pkColumn.javaField}}")
    public AjaxResult<${ClassName}Vo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return AjaxResult.success(i${ClassName}Service.queryById(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @ApiOperation("新增${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ${ClassName}Bo bo) {
        return toAjax(i${ClassName}Service.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改${functionName}
     */
    @ApiOperation("修改${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ${ClassName}Bo bo) {
        return toAjax(i${ClassName}Service.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除${functionName}
     */
    @ApiOperation("删除${functionName}")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')")
    @Log(title = "${functionName}" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(i${ClassName}Service.deleteWithValidByIds(Arrays.asList(${pkColumn.javaField}s), true) ? 1 : 0);
    }
}
