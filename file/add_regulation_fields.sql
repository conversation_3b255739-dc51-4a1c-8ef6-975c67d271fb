-- 添加法规标准相关字段到相关表

-- 1. 为 doc_version 表添加字段
ALTER TABLE doc_version 
ADD COLUMN regulation_standard_status varchar(50) NULL COMMENT '法规标准状态',
ADD COLUMN regulation_publish_date date NULL COMMENT '法规/标准发布日期',
ADD COLUMN regulation_implement_date date NULL COMMENT '法规/标准实施日期';

-- 2. 为 doc_modify_apply 表添加字段
ALTER TABLE doc_modify_apply 
ADD COLUMN regulation_standard_status varchar(50) NULL COMMENT '法规标准状态',
ADD COLUMN regulation_publish_date date NULL COMMENT '法规/标准发布日期',
ADD COLUMN regulation_implement_date date NULL COMMENT '法规/标准实施日期';

-- 3. 为 doc_standard 表添加字段
ALTER TABLE doc_standard 
ADD COLUMN regulation_standard_status varchar(50) NULL COMMENT '法规标准状态',
ADD COLUMN regulation_publish_date date NULL COMMENT '法规/标准发布日期',
ADD COLUMN regulation_implement_date date NULL COMMENT '法规/标准实施日期';

-- 4. 添加字典类型 - 法规/标准状态
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES (REPLACE(UUID(),'-',''), '法规/标准状态', 'regulation_standard_status', '0', 'admin', NOW(), 'admin', NOW(), '法规/标准状态字典');

-- 5. 添加字典数据 - 法规/标准状态选项
SET @dict_type_id = (SELECT dict_id FROM sys_dict_type WHERE dict_type = 'regulation_standard_status');

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(REPLACE(UUID(),'-',''), 1, '现行', 'current', 'regulation_standard_status', '', 'primary', 'Y', '0', 'admin', NOW(), 'admin', NOW(), '现行状态'),
(REPLACE(UUID(),'-',''), 2, '即将实施', 'upcoming', 'regulation_standard_status', '', 'warning', 'N', '0', 'admin', NOW(), 'admin', NOW(), '即将实施状态'),
(REPLACE(UUID(),'-',''), 3, '已废止', 'abolished', 'regulation_standard_status', '', 'danger', 'N', '0', 'admin', NOW(), 'admin', NOW(), '已废止状态');
