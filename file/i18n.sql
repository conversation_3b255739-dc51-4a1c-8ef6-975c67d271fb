INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dict.status_current', '现行', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dict.status_current', 'Current', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dict.status_upcoming', '即将实施', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dict.status_upcoming', 'To be Implemented', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'doc.places_select', 'Please select', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'doc.places_select', '请选择', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose_type', 'File Purpose', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose_type', '文件用途', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.mass_production_test', '产品量产测试', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.mass_production_test', 'Mass Production Test', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.product_sampling', 'Product Sampling', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.product_sampling', '产品打样', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.product_trial_mold', '产品试模', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.product_trial_mold', 'Product Trial Mold', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.trial_production_test', '产品试产测试', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose.trial_production_test', 'Trial Production Test', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
-- 字典类型国际化 - 法规/标准状态
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
    (UUID(), 'dict.regulation_standard_status', '法规/标准状态', 'zh', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50'),
    (UUID(), 'dict.regulation_standard_status', 'Regulation/Standard Status', 'en', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

-- 字段标签国际化 - 法规/标准发布日期
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
    (UUID(), 'field.regulation_publish_date', '法规/标准发布日期', 'zh', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50'),
    (UUID(), 'field.regulation_publish_date', 'Regulation/Standard Publish Date', 'en', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

-- 字段标签国际化 - 法规/标准实施日期
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
    (UUID(), 'field.regulation_implement_date', '法规/标准实施日期', 'zh', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50'),
    (UUID(), 'field.regulation_implement_date', 'Regulation/Standard Implementation Date', 'en', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

-- workflow.process_node 流程环节
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.process_node', '流程环节', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.process_node', 'Process Node', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

-- workflow.executor 执行人员
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.executor', '执行人员', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.executor', 'Executor', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');