<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>com.rzdata</groupId>
    <artifactId>dms</artifactId>
    <version>3.3.0</version>

    <name>dms</name>
    <description>体系化文件管理系统</description>

    <properties>
        <ruoyi-vue-plus.version>3.3.0</ruoyi-vue-plus.version>
        <spring-boot.version>2.5.6</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
        <druid.version>1.2.8</druid.version>
        <knife4j.version>3.0.3</knife4j.version>
        <swagger-annotations.version>1.5.22</swagger-annotations.version>
        <poi.version>4.1.2</poi.version>
        <easyexcel.version>2.2.11</easyexcel.version>
        <velocity.version>1.7</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hutool.version>5.8.29</hutool.version>
        <okhttp.version>4.9.2</okhttp.version>
        <spring-boot-admin.version>2.5.4</spring-boot-admin.version>
        <lock4j.version>2.2.1</lock4j.version>
        <dynamic-ds.version>3.4.1</dynamic-ds.version>
        <tlog.version>1.3.4</tlog.version>
        <xxl-job.version>2.3.0</xxl-job.version>

        <hashids.version>1.0.1</hashids.version>
        <poi-tl.version>1.10.0</poi-tl.version>
<!--        <spire-doc-free.version>3.9.0</spire-doc-free.version>-->
        <spire-doc.version>5.4.2</spire-doc.version>
        <caffeine.version>2.8.0</caffeine.version>
        <!-- jdk11 缺失依赖 jaxb-->
        <jaxb.version>3.0.1</jaxb.version>

        <!-- OSS 配置 -->
        <qiniu.version>7.8.0</qiniu.version>
        <aliyun.oss.version>3.13.1</aliyun.oss.version>
        <qcloud.cos.version>5.6.58</qcloud.cos.version>
        <minio.version>8.3.3</minio.version>

        <!-- docker 配置 -->
        <docker.registry.url>localhost</docker.registry.url>
        <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
        <docker.namespace>ruoyi</docker.namespace>
        <docker.plugin.version>1.2.2</docker.plugin.version>
        <blueland-workflow-sdk.version>0.1.5</blueland-workflow-sdk.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.blueland.bpmclient</groupId>
                <artifactId>workflow-sdk</artifactId>
                <version>${blueland-workflow-sdk.version}</version>
            </dependency>
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.76</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- jdk11 缺失依赖 jaxb-->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>


            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>


            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <!-- 代码生成-->
            <dependency>
                <groupId>com.rzdata</groupId>
                <artifactId>dms-generator</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.rzdata</groupId>
                <artifactId>dms-framework</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>dms-admin</module>
        <module>dms-framework</module>
        <module>dms-generator</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <!--打包该目录下的 application.yml -->
                <directory>src/main/resources</directory>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>local</profiles.active>
                <logging.level>debug</logging.level>
                <knife4j.production>false</knife4j.production>
                <endpoints.include>'*'</endpoints.include>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <logging.level>debug</logging.level>
                <knife4j.production>false</knife4j.production>
                <endpoints.include>'*'</endpoints.include>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <logging.level>warn</logging.level>
                <knife4j.production>true</knife4j.production>
                <endpoints.include>health, info, logfile</endpoints.include>
            </properties>
        </profile>
    </profiles>

</project>
