package com.rzdata.framework.enums;

/**
 * 分发类型
 */
public enum DistributeTypeEnum {
    /**
     * 个人
     */
    person( 1),
    /**
     * 部门
     */
    dept( 2),
    /**
     * 部门和个人
     */
    dept_person( 4),
    /**
     * 公司
     */
    company(8);
    private final int value;

    DistributeTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    /**
     * 增发时 培训范围融合
     */
    public static String fuse(String type1,String type2){
        DistributeTypeEnum typeEnum1 = DistributeTypeEnum.valueOf(type1);
        DistributeTypeEnum typeEnum2 = DistributeTypeEnum.valueOf(type2);
        int value1 = typeEnum1.getValue();
        int value2 = typeEnum2.getValue();
        int num = value1 + value2;
        switch (num) {
            case 3:
                return  DistributeTypeEnum.dept_person.name();
            case 2:
            case 4:
                return  type1;
            default:
                if (value1>value2){
                    return typeEnum1.name();
                }else {
                    return typeEnum2.name();
                }
        }
    }

}
