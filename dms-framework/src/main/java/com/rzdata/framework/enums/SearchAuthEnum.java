package com.rzdata.framework.enums;

/**
 * 菜单权限标识
 * 第一位：菜单权限
 * 第二位：代表状态  1=有效 2=失效
 * 第三位：代表类型  stdd：体系文件，project：项目文件，external：外来文件
 * @Author: panchichun
 * @Date: 2023/9/25
 * @Description:
 */
public enum SearchAuthEnum {

    /** 体系文件台账-公司文件 **/
    PROCESS_GS_STANDARD_LIST("process:gs:standard:list", "1", "stdd"),
    /** 体系文件台账-失效公司文件 **/
    PROCESS_SX_GS_STANDARD_LIST("process:sx:gs:standard:list", "2", "stdd"),
    /** 体系文件台账-外来文件 **/
    PROCESS_WL_STANDARD_LIST("process:wl:standard:list", "1", "external");

    private final String permission;
    private final String status;
    private final String type;

    SearchAuthEnum(String source, String status, String type) {
        this.permission = source;
        this.status = status;
        this.type = type;
    }

    public String getPermission() {
        return permission;
    }

    public String getStatus() {
        return status;
    }

    public String getType() {
        return type;
    }
}
