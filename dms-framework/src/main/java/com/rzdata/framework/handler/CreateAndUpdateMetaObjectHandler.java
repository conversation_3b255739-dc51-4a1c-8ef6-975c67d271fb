package com.rzdata.framework.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * MP注入处理器
 *
 * <AUTHOR> Li
 * @date 2021/4/25
 */
@Slf4j
public class CreateAndUpdateMetaObjectHandler implements MetaObjectHandler {

	@Override
	public void insertFill(MetaObject metaObject) {
		try {
			if (ObjectUtil.isNotNull(metaObject)) {
				if (metaObject.getOriginalObject() instanceof BaseEntity) {
					BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();
					Date current = new Date();
					// 创建时间为空 则填充
					if (ObjectUtil.isNull(baseEntity.getCreateTime())) {
						baseEntity.setCreateTime(current);
					}
					// 更新时间为空 则填充
					if (ObjectUtil.isNull(baseEntity.getUpdateTime())) {
						baseEntity.setUpdateTime(current);
					}
					String username = getLoginUsername();
					// 当前已登录 且 创建人为空 则填充
					if (StringUtils.isNotBlank(username)
							&& StringUtils.isBlank(baseEntity.getCreateBy())) {
						baseEntity.setCreateBy(username);
					}
					// 当前已登录 且 更新人为空 则填充
					if (StringUtils.isNotBlank(username)
							&& StringUtils.isBlank(baseEntity.getUpdateBy())) {
						baseEntity.setUpdateBy(username);
					}
				} else {
					this.setFieldValByName("createTime", DateUtil.date(), metaObject);
					this.setFieldValByName("updateTime", DateUtil.date(), metaObject);
				}
			}
		} catch (Exception e) {
			throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.HANDLER_AUTO_INJECTION_ERR) + " => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
		}
	}

	@Override
	public void updateFill(MetaObject metaObject) {
		try {
			if (ObjectUtil.isNotNull(metaObject) ){
				if (metaObject.getOriginalObject() instanceof BaseEntity) {
					BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();
					Date current = new Date();
					// 更新时间填充(不管为不为空)
					baseEntity.setUpdateTime(current);
					String username = getLoginUsername();
					// 当前已登录 更新人填充(不管为不为空)
					if (StringUtils.isNotBlank(username)) {
						baseEntity.setUpdateBy(username);
					}
				}
				this.setFieldValByName("updateTime", DateUtil.date(), metaObject);
			}else {
				this.setFieldValByName("updateTime", DateUtil.date(), metaObject);
			}
		} catch (Exception e) {
			throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.HANDLER_AUTO_INJECTION_ERR)+" => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
		}
	}

	/**
	 * 获取登录用户名
	 */
	private String getLoginUsername() {
		LoginUser loginUser;
		try {
			loginUser = SecurityUtils.getLoginUser();
		} catch (Exception e) {
			log.warn("自动注入警告 => 用户未登录");
			return null;
		}
		return loginUser.getUsername();
	}

}
