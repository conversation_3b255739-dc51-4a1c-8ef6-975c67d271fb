package com.rzdata.framework.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.rzdata.framework.core.domain.dto.KeyValueDto;

import java.util.*;

/**
 * 字符串工具类
 *
 * <AUTHOR> Li
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

	/**
	 * 获取参数不为空值
	 *
	 * @param value defaultValue 要判断的value
	 * @return value 返回值
	 */
	public static <T> T nvl(T value, T defaultValue) {
		return ObjectUtil.defaultIfNull(value, defaultValue);
	}

	/**
	 * 获取参数不为空值
	 *
	 * @param str defaultValue 要判断的value
	 * @return value 返回值
	 */
	public static String blankToDefault(String str, String defaultValue) {
		return StrUtil.blankToDefault(str, defaultValue);
	}

	/**
	 * * 判断一个Collection是否为空， 包含List，Set，Queue
	 *
	 * @param coll 要判断的Collection
	 * @return true：为空 false：非空
	 */
	public static boolean isEmpty(Collection<?> coll) {
		return CollUtil.isEmpty(coll);
	}

	/**
	 * * 判断一个Collection是否非空，包含List，Set，Queue
	 *
	 * @param coll 要判断的Collection
	 * @return true：非空 false：空
	 */
	public static boolean isNotEmpty(Collection<?> coll) {
		return !isEmpty(coll);
	}

	/**
	 * * 判断一个对象数组是否为空
	 *
	 * @param objects 要判断的对象数组
	 *                * @return true：为空 false：非空
	 */
	public static boolean isEmpty(Object[] objects) {
		return ArrayUtil.isEmpty(objects);
	}

	/**
	 * * 判断一个对象数组是否非空
	 *
	 * @param objects 要判断的对象数组
	 * @return true：非空 false：空
	 */
	public static boolean isNotEmpty(Object[] objects) {
		return !isEmpty(objects);
	}

	/**
	 * * 判断一个对象是否为空
	 *
	 * @param object 要判断的对象数组
	 *                * @return true：为空 false：非空
	 */
	public static boolean isEmpty(Object object) {
		return ObjectUtil.isEmpty(object);
	}

	/**
	 * * 判断一个对象是否非空
	 *
	 * @param object 要判断的对象数组
	 * @return true：非空 false：空
	 */
	public static boolean isNotEmpty(Object object) {
		return !isEmpty(object);
	}

	/**
	 * * 判断一个Map是否为空
	 *
	 * @param map 要判断的Map
	 * @return true：为空 false：非空
	 */
	public static boolean isEmpty(Map<?, ?> map) {
		return MapUtil.isEmpty(map);
	}

	/**
	 * * 判断一个Map是否为空
	 *
	 * @param map 要判断的Map
	 * @return true：非空 false：空
	 */
	public static boolean isNotEmpty(Map<?, ?> map) {
		return !isEmpty(map);
	}

	/**
	 * * 判断一个字符串是否为空串
	 *
	 * @param str String
	 * @return true：为空 false：非空
	 */
	public static boolean isEmpty(String str) {
		return StrUtil.isEmpty(str);
	}

	/**
	 * * 判断一个字符串是否为非空串
	 *
	 * @param str String
	 * @return true：非空串 false：空串
	 */
	public static boolean isNotEmpty(String str) {
		return !isEmpty(str);
	}

	/**
	 * * 判断一个对象是否为空
	 *
	 * @param object Object
	 * @return true：为空 false：非空
	 */
	public static boolean isNull(Object object) {
		return ObjectUtil.isNull(object);
	}

	/**
	 * * 判断一个对象是否非空
	 *
	 * @param object Object
	 * @return true：非空 false：空
	 */
	public static boolean isNotNull(Object object) {
		return !isNull(object);
	}

	/**
	 * * 判断一个对象是否是数组类型（Java基本型别的数组）
	 *
	 * @param object 对象
	 * @return true：是数组 false：不是数组
	 */
	public static boolean isArray(Object object) {
		return ArrayUtil.isArray(object);
	}

	/**
	 * 去空格
	 */
	public static String trim(String str) {
		return StrUtil.trim(str);
	}

	/**
	 * 截取字符串
	 *
	 * @param str   字符串
	 * @param start 开始
	 * @return 结果
	 */
	public static String substring(final String str, int start) {
		return substring(str, start, str.length());
	}

	/**
	 * 截取字符串
	 *
	 * @param str   字符串
	 * @param start 开始
	 * @param end   结束
	 * @return 结果
	 */
	public static String substring(final String str, int start, int end) {
		return StrUtil.sub(str, start, end);
	}

	/**
	 * 格式化文本, {} 表示占位符<br>
	 * 此方法只是简单将占位符 {} 按照顺序替换为参数<br>
	 * 如果想输出 {} 使用 \\转义 { 即可，如果想输出 {} 之前的 \ 使用双转义符 \\\\ 即可<br>
	 * 例：<br>
	 * 通常使用：format("this is {} for {}", "a", "b") -> this is a for b<br>
	 * 转义{}： format("this is \\{} for {}", "a", "b") -> this is \{} for a<br>
	 * 转义\： format("this is \\\\{} for {}", "a", "b") -> this is \a for b<br>
	 *
	 * @param template 文本模板，被替换的部分用 {} 表示
	 * @param params   参数值
	 * @return 格式化后的文本
	 */
	public static String format(String template, Object... params) {
		return StrUtil.format(template, params);
	}

	/**
	 * 是否为http(s)://开头
	 *
	 * @param link 链接
	 * @return 结果
	 */
	public static boolean ishttp(String link) {
		return Validator.isUrl(link);
	}

	/**
	 * 字符串转set
	 *
	 * @param str 字符串
	 * @param sep 分隔符
	 * @return set集合
	 */
	public static Set<String> str2Set(String str, String sep) {
		return new HashSet<>(str2List(str, sep, true, false));
	}

	/**
	 * 字符串转list
	 *
	 * @param str         字符串
	 * @param sep         分隔符
	 * @param filterBlank 过滤纯空白
	 * @param trim        去掉首尾空白
	 * @return list集合
	 */
	public static List<String> str2List(String str, String sep, boolean filterBlank, boolean trim) {
		List<String> list = new ArrayList<>();
		if (isEmpty(str)) {
			return list;
		}

		// 过滤空白字符串
		if (filterBlank && isBlank(str)) {
			return list;
		}
		String[] split = str.split(sep);
		for (String string : split) {
			if (filterBlank && isBlank(string)) {
				continue;
			}
			if (trim) {
				string = trim(string);
			}
			list.add(string);
		}

		return list;
	}

	/**
	 * 查找指定字符串是否包含指定字符串列表中的任意一个字符串同时串忽略大小写
	 *
	 * @param cs                  指定字符串
	 * @param searchCharSequences 需要检查的字符串数组
	 * @return 是否包含任意一个字符串
	 */
	public static boolean containsAnyIgnoreCase(CharSequence cs, CharSequence... searchCharSequences) {
		return StrUtil.containsAnyIgnoreCase(cs, searchCharSequences);
	}

	/**
	 * 驼峰转下划线命名
	 */
	public static String toUnderScoreCase(String str) {
		return StrUtil.toUnderlineCase(str);
	}

	/**
	 * 是否包含字符串
	 *
	 * @param str  验证字符串
	 * @param strs 字符串组
	 * @return 包含返回true
	 */
	public static boolean inStringIgnoreCase(String str, String... strs) {
		return StrUtil.equalsAnyIgnoreCase(str, strs);
	}

	/**
	 * 将下划线大写方式命名的字符串转换为驼峰式。如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。 例如：HELLO_WORLD->HelloWorld
	 *
	 * @param name 转换前的下划线大写方式命名的字符串
	 * @return 转换后的驼峰式命名的字符串
	 */
	public static String convertToCamelCase(String name) {
		return StrUtil.upperFirst(StrUtil.toCamelCase(name));
	}

	/**
	 * 驼峰式命名法 例如：user_name->userName
	 */
	public static String toCamelCase(String s) {
		return StrUtil.toCamelCase(s);
	}

	/**
	 * 查找指定字符串是否匹配指定字符串列表中的任意一个字符串
	 *
	 * @param str  指定字符串
	 * @param strs 需要检查的字符串数组
	 * @return 是否匹配
	 */
	public static boolean matches(String str, List<String> strs) {
		if (isEmpty(str) || isEmpty(strs)) {
			return false;
		}
		for (String pattern : strs) {
			if (isMatch(pattern, str)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 判断url是否与规则配置:
	 * ? 表示单个字符;
	 * * 表示一层路径内的任意字符串，不可跨层级;
	 * ** 表示任意层路径;
	 *
	 * @param pattern 匹配规则
	 * @param url     需要匹配的url
	 * @return
	 */
	public static boolean isMatch(String pattern, String url) {
		return ReUtil.isMatch(pattern, url);
	}

	@SuppressWarnings("unchecked")
	public static <T> T cast(Object obj) {
		return (T) obj;
	}

	/**
	 * 找出字符串中最先出现的列表中的值
	 *
	 * @param str 字符串
	 * @param list 匹配列表
	 * @return 最先出现值
	 */
	public static KeyValueDto<Integer> checkFirstStr(String str, List<String> list) {
		int minIndex = Integer.MAX_VALUE;
		String firstStr = null;
		for (String check:list) {
		  int index = str.indexOf(check);
		  if (index!=-1&& index < minIndex) {
			  minIndex = index;
			  firstStr = check;
		  }
		}
		KeyValueDto<Integer> dto = new KeyValueDto<Integer>();
		dto.setKey(firstStr);
		dto.setValue(minIndex);
		return dto;
	}
}
