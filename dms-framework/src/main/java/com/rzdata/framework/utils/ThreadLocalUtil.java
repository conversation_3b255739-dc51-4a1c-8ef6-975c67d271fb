package com.rzdata.framework.utils;

import cn.hutool.core.map.MapUtil;
import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @program: crm
 * @description:
 * @author: bigtian
 * @create: 2022-06-28 17:09
 * @since 6.0
 */
public class ThreadLocalUtil {

    public static final String LANG = "lang";

    private static TransmittableThreadLocal<Map<String, Object>> CONTEXT = new TransmittableThreadLocal() {
        /**
         * 使用TransmittableThreadLocal需要重新copy、childValue方法，因为两者都是直接返回参数的，而不是新建对象进行返回
         * 所以当执行异步线程后，main线程修改当前数据，子线程会同步更新
         * @param parentValue
         * <AUTHOR>
         * @since 6.0
         * @createTime 2022/6/29 11:28
         * @return Object
         */
        @Override
        public Object copy(Object parentValue) {
            if (parentValue instanceof Map) {
                return new LinkedHashMap<>((Map<String, Object>) parentValue);
            }
            return null;
        }

        @Override
        protected Map<String, Object> childValue(Object parentValue) {
            if (parentValue instanceof Map) {
                return new LinkedHashMap<>((Map<String, Object>) parentValue);
            }
            return null;
        }

        @Override
        protected Map<String, Object> initialValue() {
            return new LinkedHashMap<>();
        }
    };


    /**
     * 使用完之后必须调用此方法，否则可能会导致oom
     *
     * <AUTHOR>
     * @createTime 2022/6/28 17:12
     * @since 6.0
     */
    public static void remove() {
        CONTEXT.remove();
    }

    /**
     * 设置数据
     *
     * @param data
     * <AUTHOR>
     * @createTime 2022/6/28 17:12
     * @since 6.0
     */
    public static void setData(Map<String, Object> data) {
        CONTEXT.set(data);
    }

    /**
     * 获取数据
     *
     * @param key
     * @return Object
     * <AUTHOR>
     * @createTime 2022/6/28 17:11
     * @since 6.0
     */
    public static Object getValByKey(String key) {
        return CONTEXT.get().get(key);
    }

    /**
     * 获取所有的数据
     *
     * @return Map<String, Object>
     * <AUTHOR>
     * @createTime 2022/6/28 17:10
     * @since 6.0
     */
    public static Map<String, Object> getMap() {
        return CONTEXT.get();
    }

    /**
     * 获取租户
     *
     * @return String
     * <AUTHOR>
     * @createTime 2022/6/29 11:46
     * @since 6.0
     */
    public static String getTendatId() {
        return MapUtil.getStr(getMap(), "tenantid");
    }

    /**
     * 获取当前用户
     *
     * @return String
     * <AUTHOR>
     * @createTime 2022/6/29 11:46
     * @since 6.0
     */
    public static String getStaffPostCode() {
        return MapUtil.getStr(getMap(), "staffPostCode");
    }

    public static void setData(String key, Object value) {
        CONTEXT.get().put(key, value);
    }

    public static <T> T getData(String key, Class<T> clazz) {
        try {
            return (T) CONTEXT.get().getOrDefault(key, clazz.newInstance());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String getLang() {
        return MapUtil.getStr(getMap(), LANG);
    }
}
