package com.rzdata.framework.utils.sql;

import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.exception.UtilException;
import com.rzdata.framework.utils.I18nUtils;
import com.rzdata.framework.utils.StringUtils;

/**
 * sql操作工具类
 *
 * <AUTHOR>
 */
public class SqlUtil {
    /**
     * 仅支持字母、数字、下划线、空格、逗号、小数点（支持多个字段排序）
     */
    public static String SQL_PATTERN = "[a-zA-Z0-9_\\ \\,\\.]+";

    /**
     * 检查字符，防止注入绕过
     */
    public static String escapeOrderBySql(String value) {
        if (StringUtils.isNotEmpty(value) && !isValidOrderBySql(value)) {
            throw new UtilException(I18nUtils.getTitle(CommonI18nConstant.UTIL_SQL_PARAM_INCONSISTENT_STANDARD));
        }
        return value;
    }

    /**
     * 验证 order by 语法是否符合规范
     */
    public static boolean isValidOrderBySql(String value) {
        return value.matches(SQL_PATTERN);
    }
}
