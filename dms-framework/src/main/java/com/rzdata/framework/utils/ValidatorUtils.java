package com.rzdata.framework.utils;

import com.rzdata.framework.constant.CommonI18nConstant;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * Validator 校验框架工具
 *
 * <AUTHOR> Li
 */
public class ValidatorUtils {

	private static final Validator VALID = Validation.buildDefaultValidatorFactory().getValidator();

	public static <T> void validate(T object, Class<?>... groups) {
        Set<ConstraintViolation<T>> validate = VALID.validate(object, groups);
        if (!validate.isEmpty()) {
            throw new ConstraintViolationException(I18nUtils.getTitle(CommonI18nConstant.UTIL_VALIDATOR_PARAMETER_ERR), validate);
        }
    }

}
