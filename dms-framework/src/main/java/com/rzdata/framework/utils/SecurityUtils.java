package com.rzdata.framework.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.CommonI18nConstant;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.exception.ServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.Set;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    /**
     * 用户ID
     **/
    public static String getUserId() {
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.UTIL_SECURITY_GET_USER_ID_ERR), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    /**
     * 获取部门ID
     **/
    public static String getDeptId() {
        try {
            return getLoginUser().getDeptId();
        } catch (Exception e) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.UTIL_SECURITY_GET_DEPT_ID_ERR), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.UTIL_SECURITY_GET_USER_ACCOUNT_ERR), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getNickname() {
        try {
            return getLoginUser().getNickName();
        } catch (Exception e) {
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.UTIL_SECURITY_GET_USER_NICK_NAME_ERR), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }
    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            //e.printStackTrace();
            throw new ServiceException(I18nUtils.getTitle(CommonI18nConstant.UTIL_SECURITY_GET_USER_INFO_ERR), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(String userId) {
        return userId != null && ("1".equals(userId) || "2".equals(userId) || "3".equals(userId) || "2sl".equals(userId) || "4-mh".equals(userId));
    }

    /**
     * 查询当前登录用户是否有按钮权限
     */
    public static boolean getButtonPermission(String permission) {
        boolean flag = false;
        LoginUser loginUser = getLoginUser();
        if (ObjectUtil.isNull(loginUser) || CollUtil.isEmpty(loginUser.getPermissions())) {
            flag = false;
        }else{
            Set<String> authorities = loginUser.getPermissions();
            if(authorities.contains(Constants.PERMISSION_ALL)){
                flag = true;
            }else{
                flag = authorities.contains(permission);
            }
        }
        return flag;
    }

    public static void main(String[] args) {
       String password= encryptPassword("Mehow@2024");
       System.out.println(password);
    }
}
