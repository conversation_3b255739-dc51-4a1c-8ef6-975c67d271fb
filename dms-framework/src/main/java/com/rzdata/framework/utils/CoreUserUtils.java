package com.rzdata.framework.utils;

import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description 用户工具类
 * @Date 2021/7/22 1:30 下午
 * @Created BigTian
 */
@Slf4j
@Component("coreUserUtil")
public class CoreUserUtils {


    public static String getUrl() {
        return CoreUserUtils.getRequest().getRequestURI();
    }


    public static String getStaffCodeDefault(String staffCode) {
        HttpServletRequest request = getRequest();
        return Optional.ofNullable(request)
                .map(el -> el.getHeader("staffPostCode"))
                .orElse(staffCode);
    }


    /**
     * 获取当前登录人的租户
     *
     * @return String 租户编码
     * <AUTHOR>
     * @createTime 2022/5/16 10:35
     * @since 6.0
     */
    public static String getTenantId() {
        HttpServletRequest request = getRequest();
        String tenantid = Optional.ofNullable(request)
                .map(el -> el.getHeader("tenantid"))
                .orElseGet(() -> ThreadLocalUtil.getTendatId());
        return Optional.ofNullable(tenantid).orElse(Constants.TENANTID_CAM);
    }

    /**
     * 获取所有的请求头数据
     *
     * <AUTHOR>
     * @createTime 2022/6/29 13:33
     * @since 6.0
     */
    public static Map<String, Object> getHeaders() {
        HttpServletRequest request = getRequest();
        Enumeration<String> enumeration = request.getHeaderNames();
        Map<String, Object> headers = new HashMap<>();
        while (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement();
            String value = request.getHeader(name);
            headers.put(name, value);
        }
        return headers;
    }

    ///**
    // * 获取iv-userId
    // *
    // * @return
    // */
    // public static String getIvUser() {
    //    HttpServletRequest request = getRequest();
    //    return Optional.ofNullable(request)
    //            .map(el -> el.getHeader("iv-user"))
    //            .orElse("");
    //}

    /**
     * 获取当前登录人的任岗编码
     *
     * @return String 任岗编码
     * <AUTHOR>
     * @createTime 2022/5/16 10:37
     * @since 6.0
     */
    public static String getStaffPostCode() {
        return getStaffPostCode("");
    }

    public static String getStaffPostCode(String defaultCode) {
        HttpServletRequest request = getRequest();
        String staffPostCode = Optional.ofNullable(request)
                .map(el -> el.getHeader("staffPostCode"))
                .orElseGet(() -> ThreadLocalUtil.getStaffPostCode());
        return Optional.ofNullable(staffPostCode).orElse(defaultCode);
    }


    /**
     * 获取请求属性
     *
     * @return
     */
    public static ServletRequestAttributes getRequestAttr() {
        // ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);//设置子线程共享
        // return servletRequestAttributes;
        return (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    }

    /**
     * 获取request
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = getRequestAttr();
        return Optional.ofNullable(attributes).
                map(ServletRequestAttributes::getRequest)
                .orElse(null);
    }

    /**
     * 获取response
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = getRequestAttr();
        return attributes.getResponse();
    }

    /**
     * 多租户流程prodefKey组装
     */
   /* public static String appendProdefKey() {
        String tenantId = getTenantId();
        if (Constants.TENANTID_FLOW_BIO.equals(tenantId)) {
            return "";
        } else {
            return "_" + tenantId;
        }
    }*/

    /**
     * 拼接流程租户
     *
     * @param tenantId
     * @return
     */
 /*   public static String appendWorkFlowAppId(String tenantId) {
        if (StrUtil.isEmpty(tenantId) || Constants.TENANTID_FLOW_BIO.equals(tenantId)) {
            return WorkflowService.WORK_TENANTID;
        } else {
            return "CRM_" + tenantId;
        }
    }*/


    /**
     * 获取iv-userId
     *
     * @return
     */
    public static String getIvUser() {
        HttpServletRequest request = getRequest();
        return Optional.ofNullable(request)
                .map(el -> el.getHeader("iv-user"))
                .orElse("luob");
        //.orElseThrow(() -> new BusinessException("请求头中没有iv-user"));
    }

    public static String getDeptCode(String deptCode) {
        String code = "";
        if (ObjectUtil.isNotEmpty(deptCode)) {
            code = deptCode;
            if (Objects.equals(getTenantId(), Constants.TENANTID_CAM)) {
                if (deptCode.length() > 15) {
                    code = deptCode.substring(0, 15);
                }
            } else {
                if (deptCode.length() > 12) {
                    code = deptCode.substring(0, 12);
                }
            }
        }
        return code;
    }

    public static int deptCodeLength(String tenantId) {
        if (Objects.equals(Constants.TENANTID_CAM, tenantId)) {
            return 15;
        } else {
            return 12;
        }
    }

    public static String getLang() {
        try {
            HttpServletRequest request = getRequest();
            if (ObjectUtil.isNull(request)) {
                return ObjectUtil.defaultIfNull(ThreadLocalUtil.getLang(),"zh");
            }
            return ObjectUtil.defaultIfNull(request.getLocale().getLanguage(),"zh");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
