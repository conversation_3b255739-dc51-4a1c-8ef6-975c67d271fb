package com.rzdata.framework.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.rzdata.framework.domain.International;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国际化工具类
 */
public class I18nUtils {

    public static final String INVOICE_BACK = "back";

    private static final RedisUtils REDIS_UTILS = SpringUtil.getBean(RedisUtils.class);

    /**
     * 如果为空则抛出异常
     *
     * @param code 编码
     * @return {@link String}
     */
    public static String getTitle(String code) {
        International international = getByCode(code, INVOICE_BACK);
        return ObjectUtil.isNull(international) ? code : international.getName();
    }

    public static Map<String,String> getTitles(Collection<String> codes) {
        List<International> list = getByCodeList(codes, INVOICE_BACK);

        return list.stream()
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toMap(International::getCode, International::getName));
    }

    /**
     * 自动渲染国际化
     *
     * @param code
     * @param params
     * @return
     */
    public static String getTitleFormat(String code, Object... params) {
        String title = getTitle(code);
        return StrUtil.format(title, params);
    }


    private static International getByCode(String code, String type) {
        return REDIS_UTILS.hget(getRedisKey(), getHashKey(code, type), International.class);
    }

    private static List<International> getByCodeList(Collection<String> codes, String type) {
        return REDIS_UTILS.hmget(getRedisKey(), getHashKeys(codes, type), International.class);
    }

    private static String getRedisKey() {
        return getI18nKey(CoreUserUtils.getLang());
    }

    private static String getHashKey(String code, String type) {
        return StrUtil.format("{}:{}", code, type);
    }

    private static List<String> getHashKeys(Collection<String> codes, String type) {
        List<String> list = new ArrayList<>();
        for (String code : codes) {
            list.add(StrUtil.format("{}:{}", code, type));
        }
        return list;
    }

    public static String getI18nKey(String lang) {
        return CoreUserUtils.getTenantId() +":i18n:"+ lang;
    }
}
