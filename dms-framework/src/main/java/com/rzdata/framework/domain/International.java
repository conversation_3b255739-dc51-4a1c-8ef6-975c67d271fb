package com.rzdata.framework.domain;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.util.Date;

/**
 * 国际化 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ApiModel(value = "国际化")
@Alias("Internationals")
public class International {


    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "编码", required = true)
    private String code;

    @ApiModelProperty(value = "名称", required = true)
    private String name;

    @ApiModelProperty(value = "语言", required = true)
    private String lang;

    @ApiModelProperty(value = "类型", required = true)
    private String type;

    @ApiModelProperty(value = "租户id(后台赋值)")
    private String tenantId;

    private String createBy;
    private String updateBy;
    private Date updateTime;
    private Date createTime;
    @ApiModelProperty(value = "分页大小")
    protected int pageSize;

    @ApiModelProperty(value = "分页页码")
    protected int pageNum;
}
