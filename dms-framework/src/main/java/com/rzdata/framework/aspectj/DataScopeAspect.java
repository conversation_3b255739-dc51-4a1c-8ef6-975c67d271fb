package com.rzdata.framework.aspectj;

import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.domain.entity.SysRole;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.service.UserService;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.reflect.ReflectUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 数据过滤处理
 *
 * <AUTHOR> Li
 */
@Aspect
@Component
public class DataScopeAspect {

	/**
	 * 全部数据权限
	 */
	public static final String DATA_SCOPE_ALL = "1";

	/**
	 * 自定数据权限
	 */
	public static final String DATA_SCOPE_CUSTOM = "2";

	/**
	 * 部门数据权限
	 */
	public static final String DATA_SCOPE_DEPT = "3";

	/**
	 * 部门及以下数据权限
	 */
	public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

	/**
	 * 仅本人数据权限
	 */
	public static final String DATA_SCOPE_SELF = "5";

	/**
	 * 数据权限过滤关键字
	 */
	public static final String DATA_SCOPE = "dataScope";

	/**
	 * 数据权限过滤关键字
	 */
	public static final String DATA_SCOPE_VALUE = "dataScopeAuth";

	@Before("@annotation(controllerDataScope)")
	public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable {
		clearDataScope(point);
		handleDataScope(point, controllerDataScope);
	}

	protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope) {
		// 获取当前的用户
		LoginUser loginUser = SecurityUtils.getLoginUser();
		if (StringUtils.isNotNull(loginUser)) {
			SysUser currentUser = SpringUtils.getBean(UserService.class).selectUserById(loginUser.getUserId());
            // 如果是超级管理员，则不过滤数据
			if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin()) {
				dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(),
					controllerDataScope.userAlias(), controllerDataScope.isUser());
			}
		}
	}

	/**
	 * 数据范围过滤
	 *
	 * @param joinPoint 切点
	 * @param user      用户
	 * @param userAlias 别名
	 */
	public static void dataScopeFilter(JoinPoint joinPoint, SysUser user, String deptAlias, String userAlias, boolean isUser) {
		StringBuilder sqlString = new StringBuilder();

		// 将 "." 提取出,不写别名为单表查询,写别名为多表查询
		deptAlias = StringUtils.isNotBlank(deptAlias) ? deptAlias + "." : "";
		userAlias = StringUtils.isNotBlank(userAlias) ? userAlias + "." : "";

		for (SysRole role : user.getRoles()) {
			String dataScope = role.getDataScope();
			if (DATA_SCOPE_ALL.equals(dataScope)) {
				sqlString = new StringBuilder();
				break;
			} else if (DATA_SCOPE_CUSTOM.equals(dataScope)) {
				sqlString.append(StringUtils.format(
					" OR {}dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ",
					deptAlias, role.getRoleId()));
			} else if (DATA_SCOPE_DEPT.equals(dataScope)) {
				sqlString.append(StringUtils.format(" OR {}dept_id = '{}' ",
					deptAlias, user.getDeptId()));
			} else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope)) {
				sqlString.append(StringUtils.format(
					" OR {}dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )",
					deptAlias, user.getDeptId(), user.getDeptId()));
			} else if (DATA_SCOPE_SELF.equals(dataScope)) {
				if (isUser) {
					sqlString.append(StringUtils.format(" OR {}user_id = '{}' ",
						userAlias, user.getUserId()));
				} else {
					// 数据权限为仅本人且没有userAlias别名不查询任何数据
					sqlString.append(" OR 1=0 ");
				}
			}
			//查询到最大的权限就退出
			break;
		}

		if (StringUtils.isNotBlank(sqlString.toString())) {
			putDataScope(joinPoint, sqlString.substring(4));
		} else {
			Object params = joinPoint.getArgs()[0];
			if (StringUtils.isNotNull(params)) {
				if (params instanceof BaseEntity) {
					BaseEntity baseEntity = (BaseEntity) params;
					baseEntity.getParams().put(DATA_SCOPE_VALUE, "dataScopeValue");
				} else {
					Map<String, Object> invoke = ReflectUtils.invokeGetter(params, "params");
					invoke.put(DATA_SCOPE_VALUE, "dataScopeValue");
				}
			}
		}
	}

	/**
	 * 拼接权限sql前先清空params.dataScope参数防止注入
	 */
	private void clearDataScope(final JoinPoint joinPoint) {
		Object params = joinPoint.getArgs()[0];
		if (StringUtils.isNotNull(params)) {
			putDataScope(joinPoint, "");
		}
	}

	private static void putDataScope(JoinPoint joinPoint, String sql) {
		Object params = joinPoint.getArgs()[0];
		if (StringUtils.isNotNull(params)) {
			if (params instanceof BaseEntity) {
				BaseEntity baseEntity = (BaseEntity) params;
				baseEntity.getParams().put(DATA_SCOPE, sql);
			} else {
				Map<String, Object> invoke = ReflectUtils.invokeGetter(params, "params");
				invoke.put(DATA_SCOPE, sql);
			}
		}
	}
}
