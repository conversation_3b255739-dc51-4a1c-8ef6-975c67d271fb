package com.rzdata.framework.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.framework.core.domain.MenuTreeEntity;
import com.rzdata.framework.core.domain.TreeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 菜单权限表 sys_menu
 *
 * <AUTHOR> Li
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_menu")
@ApiModel("菜单权限业务对象")
public class SysMenu extends MenuTreeEntity {

	/**
	 * 菜单ID
	 */
	@ApiModelProperty(value = "菜单ID")
	@TableId(value = "menu_id")
	private Long menuId;

	/**
	 * 菜单名称
	 */
	@ApiModelProperty(value = "菜单名称")
	@NotBlank(message = "菜单名称不能为空")
	@Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
	private String menuName;

	/**
	 * 显示顺序
	 */
	@ApiModelProperty(value = "显示顺序")
	@NotBlank(message = "显示顺序不能为空")
	private String orderNum;

	/**
	 * 路由地址
	 */
	@ApiModelProperty(value = "路由地址")
	@Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
	private String path;

	/**
	 * 组件路径
	 */
	@ApiModelProperty(value = "组件路径")
	@Size(min = 0, max = 200, message = "组件路径不能超过255个字符")
	private String component;

    /**
     * 路由参数
     */
	@ApiModelProperty(value = "路由参数")
    private String query;

	/**
	 * 是否为外链（0是 1否）
	 */
	@ApiModelProperty(value = "是否为外链（0是 1否）")
	private String isFrame;

	/**
	 * 是否缓存（0缓存 1不缓存）
	 */
	@ApiModelProperty(value = "是否缓存（0缓存 1不缓存）")
	private String isCache;

	/**
	 * 类型（M目录 C菜单 F按钮）
	 */
	@ApiModelProperty(value = "类型（M目录 C菜单 F按钮）")
	@NotBlank(message = "菜单类型不能为空")
	private String menuType;

	/**
	 * 显示状态（0显示 1隐藏）
	 */
	@ApiModelProperty(value = "显示状态（0显示 1隐藏）")
	private String visible;

	/**
	 * 菜单状态（0显示 1隐藏）
	 */
	@ApiModelProperty(value = "菜单状态（0显示 1隐藏）")
	private String status;

	/**
	 * 权限字符串
	 */
	@ApiModelProperty(value = "权限字符串")
	@Size(min = 0, max = 100, message = "权限标识长度不能超过100个字符")
	private String perms;

	/**
	 * 菜单图标
	 */
	@ApiModelProperty(value = "菜单图标")
	private String icon;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
