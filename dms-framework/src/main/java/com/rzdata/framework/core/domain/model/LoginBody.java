package com.rzdata.framework.core.domain.model;

import com.rzdata.framework.enums.ApiTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户登录对象
 *
 * <AUTHOR> Li
 */

@Data
@Accessors(chain = true)
@ApiModel("用户登录对象")
public class LoginBody {

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String password;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String code;

    /**
     * 唯一标识
     */
    @ApiModelProperty(value = "唯一标识")
    private String uuid = "";

    /**
     * 租户Id
     */
    @ApiModelProperty(value = "租户Id")
    private String tenantId = "";

    /**
     * 请求类型 移动端 PC端
     */
    @ApiModelProperty(value = "请求类型 移动端 PC端")
    private ApiTypeEnum type;

}
