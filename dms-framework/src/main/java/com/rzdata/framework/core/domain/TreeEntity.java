package com.rzdata.framework.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * Tree基类
 *
 * <AUTHOR> Li
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TreeEntity extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 父菜单名称
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "父菜单名称")
	private String parentName;

	/**
	 * 父菜单ID
	 */
	@ApiModelProperty(value = "父菜单ID")
	private String parentId;

	/**
	 * 子部门
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "子部门")
	private List<?> children = new ArrayList<>();

}
