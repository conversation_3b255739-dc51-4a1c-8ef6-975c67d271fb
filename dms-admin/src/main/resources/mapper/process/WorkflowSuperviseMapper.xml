<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.WorkflowSuperviseMapper">

    <resultMap type="com.rzdata.process.domain.vo.WorkflowSuperviseVo" id="WorkflowSuperviseResult">
        <result property="id" column="id"/>
        <result property="procDefKey" column="proc_def_key"/>
        <result property="procDefId" column="proc_def_id"/>
        <result property="procDefName" column="proc_def_name"/>
        <result property="supervise" column="supervise"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryActReProcDefList" resultType="com.rzdata.process.domain.vo.WorkflowSuperviseVo">
        SELECT
            a.id_ as proc_def_id,
            a.key_ as proc_def_key,
            a.name_ as proc_def_name
        FROM
            ${bpmDataSource}.act_re_procdef a
                LEFT JOIN ${bpmDataSource}.act_re_procdef b ON a.key_ = b.key_
                AND b.version_ > a.version_
        WHERE
            b.key_ IS NULL and a.TENANT_ID_ = #{tenantId};
    </select>

    <select id="queryUniteworkTaskList" resultType="com.rzdata.process.domain.vo.UniteworkTaskVo">
        SELECT
        t1.UNITE_PROCDEFNAME as proc_def_name,
        t1.UNITE_TITLE as title,
        t1.UNITE_CUR_ACTDEFNAME as cur_act_name,
        t1.UNITE_START_USERNAME as start_user_name,
        t1.UNITE_SEND_TIME as send_time,
        t1.UNITE_REC_USERID as recUserId,
        t1.UNITE_URL as url,
        t2.doc_id,
        t2.id as appId,
        t2.apply_class as applyClass,
        t2.version_value,
        t1.UNITE_STATUS as status,
        t1. UNITE_PROCINSTID as uniteProcinstid
        FROM
        ${bpmDataSource}.T_UNITEWORK_HISTORY t1
        LEFT JOIN doc_workflow_apply_log t2 ON t1.UNITE_PROCINSTID = t2.proc_inst_id
        <where>
            <if test="bo.status != null and bo.status != ''">
                and t1.UNITE_STATUS = #{bo.status}
            </if>
            <if test="bo.recUserId != null and bo.recUserId != ''">
                and t1.UNITE_REC_USERID = #{bo.recUserId}
            </if>
            <if test="bo.procDefName != null and bo.procDefName != ''">
                and t1.UNITE_PROCDEFNAME = #{bo.procDefName}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and t1.UNITE_PROCDEFKEY = #{bo.procDefKey}
            </if>
            <if test="bo.title != null and bo.title != ''">
                and t1.UNITE_TITLE like concat('%', #{bo.title}, '%')
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and t1.UNITE_SEND_TIME &lt;= #{bo.params.endTime}
            </if>
        </where>
        ORDER BY t1.UNITE_SEND_TIME desc
    </select>

</mapper>
