<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocDistributeLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.DocDistributeLogVo" id="DocDistributeLogResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="docId" column="doc_id"/>
        <result property="recordDocId" column="record_doc_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="nums" column="nums"/>
        <result property="receiveUserName" column="receive_user_name"/>
        <result property="distributeTime" column="distribute_time"/>
        <result property="distributeUserName" column="distribute_user_name"/>
        <result property="receiveStatus" column="receive_status"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="expiration" column="expiration"/>
        <result property="userName" column="user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="fileId" column="file_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="versionStatus" column="version_status"/>
        <result property="deptName" column="dept_name"/>
        <result property="dbStatus" column="db_status"/>
        <result property="changeType" column="changeType"/>
        <result property="docClassName" column="docClassName"/>
        <result property="nickName" column="nick_name"/>
        <result property="processStatus" column="processStatus"/>
        <result property="receiveNickName" column="receive_nick_name"/>
        <result property="mergeFileId" column="merge_file_id"/>
        <result property="encryptFileId" column="encrypt_file_id"/>
        <result property="procInstId" column="proc_inst_id"/>
        <result property="reviewTime" column="review_time"/>
        <result property="changeType" column="change_type"/>
        <result property="compileDeptId" column="compile_dept_id"/>
        <result property="compileDeptName" column="compile_dept_name"/>
        <result property="totalNums" column="total_nums"/>

    </resultMap>

    <select id="selectDocDistributeLogPage" resultMap="DocDistributeLogResult">
        SELECT l.*,
        s.doc_class,
        s.doc_name,
        s.expiration,
        v.user_name,
        v.apply_time,
        v.file_id,
        v.merge_file_id,
        v.encrypt_file_id,
        v.version_value,
        v.start_date,
        v.end_date,
        v.status as version_status,
        d.dept_name,
        ca.change_type as changeType,
        su.nick_name,
        su2.nick_name as receive_nick_name,
        if(l.doc_id in (SELECT doc_id from doc_workflow_apply_log where act_def_name != '结束' and doc_id is not null), 1,
        0) as processStatus
        FROM doc_distribute_log l
        LEFT JOIN doc_standard s ON l.doc_id = s.id
        LEFT JOIN doc_version v ON l.version_id = v.id
        LEFT JOIN sys_dept d ON v.dept_id = d.dept_id
        LEFT JOIN doc_modify_apply ca ON l.apply_id = ca.id
        LEFT JOIN sys_user su ON ca.user_name = su.user_name
        LEFT JOIN sys_user su2 ON l.receive_user_name = su2.user_name
        <where>

            <if test='bo.searchType == "1"'>
                and v.dept_id = #{bo.deptId}
                and l.dept_id = #{bo.deptId}
            </if>
            <if test='bo.searchType == "2"'>
                and v.dept_id != #{bo.deptId}
                and l.dept_id != #{bo.deptId}
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and l.doc_id = #{bo.docId}
            </if>
            <if test="bo.dbStatus != null and bo.dbStatus != ''">
                and l.db_status = #{bo.dbStatus}
            </if>
            <if test="bo.receiveStatus != null and bo.receiveStatus != ''">
                and l.receive_status = #{bo.receiveStatus}
            </if>
            <if test="bo.printStatus != null and bo.printStatus != ''">
                and l.print_status = #{bo.printStatus}
            </if>
            <if test="bo.linkType != '' and bo.linkType != null">
                and s.doc_class = #{bo.linkType}
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                and s.doc_class = #{bo.docClass}
            </if>
            <if test="bo.deptId != '' and bo.deptId != null">
                and v.dept_id = #{bo.deptId}
            </if>
            <if test="bo.validDate != '' and bo.validDate != null">
                and s.create_time &gt; #{bo.validDate}
            </if>
            <if test="bo.receiveUserName != '' and bo.receiveUserName != null">
                and l.receive_user_name = #{bo.receiveUserName}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and s.doc_name LIKE CONCAT('%', #{bo.docName} ,'%')
            </if>

            <if test="bo.startTime != null and bo.startTime != ''">
                and v.start_date &gt;= #{bo.startTime}
            </if>
            <if test="bo.endTime != null and bo.endTime != ''">
                and v.start_date &lt;= #{bo.endTime}
            </if>
        </where>
        ORDER BY l.receive_time DESC ,l.create_time desc
    </select>

    <select id="selectDocDistributeLog4Page" resultMap="DocDistributeLogResult">
        SELECT
        s.doc_class,
        s.doc_name,
        s.expiration,
        v.user_name,
        v.apply_time,
        v.file_id,
        v.merge_file_id,
        v.encrypt_file_id,
        v.version_value,
        v.start_date,
        v.end_date,
        v.status as version_status,
        v.review_time,
        d.dept_name,
        d.dept_id,
        su.nick_name,
        l.doc_id,
        l.version_id,
        wk.proc_inst_id,
        <choose>
            <!--根据数据签收分发表和签收明细表判断签收的状态-->
            <when test="bo.params.dataScopeAuth!='dataScopeValue'">
                (select 2 from dual where (select sum(nums) from doc_distribute_log dl
                join sys_dept d on dl.dept_id=d.dept_id
                where dl.apply_id=l.id and dl.doc_id=l.doc_id
                <!-- 数据范围过滤 -->
                <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                    AND ( ${bo.params.dataScope} )
                </if>

                <!--数据状态-->
                <if test="bo.dbStatus != null and bo.dbStatus != ''">
                    and dl.db_status = #{bo.dbStatus}
                </if>
                group by dl.apply_id
                )
                &lt;=
                ifnull((select count(1) from doc_distribute_item dl
                join sys_dept d on dl.dept_id=d.dept_id
                where dl.apply_id=l.id and dl.doc_id=l.doc_id
                <!--doc_distribute_log表的分发部门-->
                <!-- 数据范围过滤 -->
                <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                    AND ( ${bo.params.dataScope} )
                </if>
                group by dl.apply_id),0)) isSign,
            </when>

            <otherwise>
                2 isSign,
            </otherwise>
        </choose>

        <choose>
            <when test="bo.source=='sign'">
                <choose>
                    <when test="bo.params.dataScopeAuth!='dataScopeValue'">
                        ${bo.receiveStatus} as receive_status,
                    </when>
                    <otherwise>
                        1 as receive_status,
                    </otherwise>
                </choose>
                <!--分发数量，查询数据的分发数-->
                (select sum(nums) from doc_distribute_log dl

                join sys_dept d on dl.dept_id=d.dept_id where dl.apply_id=l.id

                <!-- 数据范围过滤 -->
                <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                    AND ( ${bo.params.dataScope} )
                </if>

                group by dl.apply_id) as nums,
            </when>
            <otherwise>
                <choose>
                    <!--文件的签收状态-->
                    <when test="bo.params.dataScopeAuth!='dataScopeValue'">
                        (select receive_status from doc_distribute_log dl where dl.apply_id=l.id
                        and receive_status='1'
                        <if test="bo.dbStatus != null and bo.dbStatus != ''">
                            and dl.db_status = #{bo.dbStatus}
                        </if>

                        <if test="bo.deptId != '' and bo.deptId != null">
                            and dl.dept_id = #{bo.deptId}
                        </if>
                        group by dl.apply_id) as receive_status,
                    </when>
                    <otherwise>
                        1 as receive_status,
                    </otherwise>
                </choose>

                <!--分发数量-->
                (select sum(nums) from doc_distribute_log dl join sys_dept d on dl.dept_id=d.dept_id
                where dl.apply_id=l.id

                <!-- 数据范围过滤 -->
                <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                    AND ( ${bo.params.dataScope} )
                </if>

                group by dl.apply_id) as nums,
            </otherwise>
        </choose>

        <if test="bo.receiveStatus != null and bo.receiveStatus != ''">
            <!--子查询一些字段，可以单独在程序中查询，后续修改-->
            (select receive_time from doc_distribute_log dl where dl.apply_id=l.id
            and dl.receive_status = #{bo.receiveStatus} group by dl.apply_id) as receive_time,

            (select create_time from doc_distribute_log dl where dl.apply_id=l.id
            and dl.receive_status = #{bo.receiveStatus} group by dl.apply_id) as create_time,

            (select max(dl.distribute_user_nick_name) from doc_distribute_log dl
            where dl.apply_id=l.id and dl.receive_status = #{bo.receiveStatus} group by dl.apply_id)
            as distributeUserNickName,

            (select max(su.nick_name)
            from doc_distribute_log dl JOIN sys_user su ON dl.receive_user_name = su.user_name
            where dl.apply_id=l.id and dl.receive_status = #{bo.receiveStatus} group by dl.apply_id)
            as receive_nick_name,
        </if>

        l.id as id,

        if(l.doc_id in (SELECT doc_id from doc_workflow_apply_log where act_def_name != '结束' and doc_id is not null), 1,
        0) as processStatus
        FROM doc_modify_apply l
        LEFT JOIN doc_standard s ON l.doc_id = s.id
        LEFT JOIN doc_version v ON l.version_id = v.id
        LEFT JOIN sys_dept d ON l.dept_id = d.dept_id
        LEFT JOIN sys_user su ON l.user_name = su.user_name
        LEFT JOIN doc_workflow_apply_log wk ON wk.id = l.id
        <where>
            <choose>
                <!--非本部门，外部门菜单查询条件-->
                <when test="bo.source=='notMyDept'">
                    <if test="bo.deptId != '' and bo.deptId != null">
                        and v.dept_id != #{bo.deptId}
                    </if>

                    <!---判断数据是否生效分发-->
                    and exists (select 1 from doc_distribute_log dl where dl.apply_id=l.id
                    <if test="bo.dbStatus != null and bo.dbStatus != ''">
                        and dl.db_status = #{bo.dbStatus}
                    </if>

                    <!--doc_distribute_log表的分发部门-->
                    <if test="bo.distributeDeptId != '' and bo.distributeDeptId != null">
                        and dl.dept_id = #{bo.distributeDeptId}
                    </if>
                    )
                </when>

                <!--文件打印-->
                <when test="bo.source=='print'">
                    <!--doc_distribute_log表的分发部门-->
                    <!---判断数据是否生效分发-->
                    and exists (select 1 from doc_distribute_log dl join sys_dept d on dl.dept_id=d.dept_id
                    where dl.apply_id=l.id
                    <!--doc_distribute_log表的分发部门-->
                    <!-- 数据范围过滤 -->
                    <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                        AND ( ${bo.params.dataScope} )
                    </if>

                    <if test="bo.dbStatus != null and bo.dbStatus != ''">
                        and dl.db_status = #{bo.dbStatus}
                    </if>
                    )

                    <!---新增已打印的逻辑，判断是否已打印 ，根据分发数和打印数的大小判断-->
                    <if test="bo.printStatus == 1">
                        and (select sum(nums) from doc_distribute_log dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.apply_id=l.id and dl.doc_id=l.doc_id
                        <!-- 数据范围过滤 -->
                        <if test="bo.receiveStatus != null and bo.receiveStatus != ''">

                            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                                AND ( ${bo.params.dataScope} )
                            </if>
                        </if>

                        <if test="bo.dbStatus != null and bo.dbStatus != ''">
                            and dl.db_status = #{bo.dbStatus}
                        </if>
                        group by dl.apply_id
                        )
                        &lt;=
                        ifnull((select count(1) from doc_print_log dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.distribute_id=l.id
                        <!-- 数据范围过滤 -->
                        <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                            AND ( ${bo.params.dataScope} )
                        </if>
                        group by dl.distribute_id),0)
                    </if>

                    <!---新增未打印的逻辑，判断数据未打印，根据分发数和打印数的大小判断 -->
                    <if test="bo.printStatus == 0">
                        and (select sum(nums) from doc_distribute_log dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.apply_id=l.id and dl.doc_id=l.doc_id
                        <!-- 数据范围过滤 -->
                        <if test="bo.receiveStatus != null and bo.receiveStatus != ''">

                            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                                AND ( ${bo.params.dataScope} )
                            </if>
                        </if>

                        <if test="bo.dbStatus != null and bo.dbStatus != ''">
                            and dl.db_status = #{bo.dbStatus}
                        </if>
                        group by dl.apply_id
                        )
                        &gt;
                        ifnull((select count(1) from doc_print_log dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.distribute_id=l.id
                        <!-- 数据范围过滤 -->
                        <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                            AND ( ${bo.params.dataScope} )
                        </if>
                        group by dl.distribute_id),0)
                    </if>


                </when>

                <!---判断数据是否生效分发-->
                <when test="bo.source=='sign'">
                    and exists (select 1 from doc_distribute_log dl
                    join sys_dept d on dl.dept_id=d.dept_id
                    where dl.apply_id=l.id
                    <if test="bo.receiveStatus != null and bo.receiveStatus != ''">

                        and dl.receive_status = #{bo.receiveStatus}

                        <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                            AND ( ${bo.params.dataScope} )
                        </if>
                    </if>

                    <if test="bo.dbStatus != null and bo.dbStatus != ''">
                        and dl.db_status = #{bo.dbStatus}
                    </if>
                    )

                    <!---新增的已签收的逻辑，根据 doc_distribute_log和doc_distribute_item的数量大小判断，
                    因为考虑公司文件管理员的菜单-->
                    <if test="bo.receiveStatus == 1">
                        and (select sum(nums) from doc_distribute_log dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.apply_id=l.id and dl.doc_id=l.doc_id
                        <!--doc_distribute_log表的分发部门-->
                        <!-- 数据范围过滤 -->
                        <if test="bo.receiveStatus != null and bo.receiveStatus != ''">

                            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                                AND ( ${bo.params.dataScope} )
                            </if>
                        </if>

                        <if test="bo.dbStatus != null and bo.dbStatus != ''">
                            and dl.db_status = #{bo.dbStatus}
                        </if>
                        group by dl.apply_id
                        )
                        &lt;=
                        ifnull((select count(1) from doc_distribute_item dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.apply_id=l.id and dl.doc_id=l.doc_id
                        <!--doc_distribute_log表的分发部门-->
                        <!-- 数据范围过滤 -->
                        <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                            AND ( ${bo.params.dataScope} )
                        </if>
                        group by dl.apply_id),0)
                    </if>

                    <!---新增的没有签收的逻辑，根据 doc_distribute_log和doc_distribute_item的数量大小判断，
                    因为要考虑公司文件管理员的菜单 -->
                    <if test="bo.receiveStatus == 0">
                        and (select sum(nums) from doc_distribute_log dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.apply_id=l.id and dl.doc_id=l.doc_id
                        <!--doc_distribute_log表的分发部门-->
                        <!-- 数据范围过滤 -->
                        <if test="bo.receiveStatus != null and bo.receiveStatus != ''">

                            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                                AND ( ${bo.params.dataScope} )
                            </if>
                        </if>

                        <if test="bo.dbStatus != null and bo.dbStatus != ''">
                            and dl.db_status = #{bo.dbStatus}
                        </if>
                        group by dl.apply_id
                        )
                        &gt;
                        ifnull((select count(1) from doc_distribute_item dl
                        join sys_dept d on dl.dept_id=d.dept_id
                        where dl.apply_id=l.id and dl.doc_id=l.doc_id
                        <!--doc_distribute_log表的分发部门-->
                        <!-- 数据范围过滤 -->
                        <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                            AND ( ${bo.params.dataScope} )
                        </if>
                        group by dl.apply_id),0)
                    </if>
                </when>

                <when test="bo.source=='dept'">
                    <!--本部门文件，本部门文件菜单的判断条件-->
                    and exists (select 1 from doc_distribute_log dl where dl.apply_id=l.id
                    and dl.db_status = #{bo.dbStatus} and dl.dept_id = #{bo.deptId}
                    )

                    <if test="bo.deptId != '' and bo.deptId != null">
                        and l.dept_id = #{bo.deptId}
                    </if>
                </when>

                <otherwise>
                    <!--其他-->
                    <!-- 数据范围过滤 -->
                    <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                        AND ( ${bo.params.dataScope} )
                    </if>

                    <if test="bo.dbStatus != null and bo.dbStatus != ''">
                        and exists (select 1 from doc_distribute_log dl where dl.apply_id=l.id
                        and dl.db_status = #{bo.dbStatus}
                        )
                    </if>
                </otherwise>
            </choose>

            <if test="bo.docId != null and bo.docId != ''">
                and l.doc_id = #{bo.docId}
            </if>

            <if test="bo.linkType != '' and bo.linkType != null">
                and s.doc_class = #{bo.linkType}
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                and s.doc_class = #{bo.docClass}
            </if>

            <if test="bo.validDate != '' and bo.validDate != null">
                and s.create_time &gt; #{bo.validDate}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and s.doc_name LIKE CONCAT('%', #{bo.docName} ,'%')
            </if>

            <if test="bo.startTime != null and bo.startTime != ''">
                and v.start_date &gt;= #{bo.startTime}
            </if>
            <if test="bo.endTime != null and bo.endTime != ''">
                and v.start_date &lt;= #{bo.endTime}
            </if>
        </where>
        ORDER BY l.apply_time DESC
    </select>

    <select id="queryDocDistributeLogListVoByAppId" resultMap="DocDistributeLogResult" parameterType="java.util.Map">
        select l.doc_name,
               l.doc_id,
               l.doc_class,
               l.version_id,
               l.version_value,

               l.create_time,
               l.nums,
               t.dept_name,
               l.dept_id

        from doc_distribute_log l
                 join sys_dept t on l.dept_id = t.dept_id
        where l.apply_id = (select apply_id
                            from doc_change_apply d

                            where d.doc_id = (SELECT doc_id from doc_modify_apply a where a.id = #{applyId})
                              and change_type = 'DISUSE')
    </select>

    <select id="selectDistributeNoProcessPage" resultMap="DocDistributeLogResult">
        SELECT l.*,
        s.expiration,
        v.apply_time,
        s.file_id,
        v.start_date,
        v.end_date,
        v.status as version_status
        FROM doc_distribute_log l
        LEFT JOIN doc_version v ON l.version_id = v.id
        LEFT JOIN doc_standard s ON v.standard_id = s.id
        <where>
            l.dept_id = #{bo.deptId}
            and s.lock_status = 0 and v.status = 1
            <if test='bo.searchType == "1"'>
                and v.dept_id = #{bo.deptId}
            </if>
            <if test='bo.searchType == "2"'>
                and v.dept_id != #{bo.deptId}
            </if>
            <if test="bo.dbStatus != null and bo.dbStatus != ''">
                and l.db_status = #{bo.dbStatus}
            </if>
            <if test="bo.receiveStatus != null and bo.receiveStatus != ''">
                and l.receive_status = #{bo.receiveStatus}
            </if>
            <if test="bo.printStatus != null and bo.printStatus != ''">
                and l.print_status = #{bo.printStatus}
            </if>
            <if test="bo.linkType != '' and bo.linkType != null">
                and s.doc_class = #{bo.linkType}
            </if>
            <if test="bo.receiveUserName != '' and bo.receiveUserName != null">
                and l.receive_user_name = #{bo.receiveUserName}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                s.doc_class = #{bo.searchValue}
                OR s.doc_name LIKE CONCAT('%', #{bo.searchValue} ,'%')
                OR v.user_name LIKE CONCAT('%', #{bo.searchValue} ,'%')
                )
            </if>
        </where>
        GROUP BY l.doc_id,l.version_id
        ORDER BY l.receive_time DESC ,l.create_time desc
    </select>

    <update id="updateByDocId">
        update doc_distribute_log
        set db_status = 1
        where doc_id = #{docId}
    </update>

    <select id="selectDocDistributeLogList" resultMap="DocDistributeLogResult">
        SELECT l.*,
        s.doc_class,
        s.doc_name,
        s.expiration,
        v.user_name,
        v.apply_time,
        s.file_id,
        v.version_value,
        v.start_date,
        v.end_date,
        v.status as version_status,
        d.dept_name,
        ca.change_type as changeType,
        dc.class_name as docClassName
        FROM doc_distribute_log l
        LEFT JOIN doc_standard s ON l.doc_id = s.id
        LEFT JOIN doc_version v ON l.version_id = v.id
        LEFT JOIN sys_dept d ON v.dept_id = d.dept_id
        LEFT JOIN doc_modify_apply ca ON l.apply_id = ca.id
        LEFT JOIN basic_doc_class dc ON s.doc_class = dc.id
        <where>
            l.dept_id = #{bo.deptId}
            <if test='bo.searchType == "1"'>
                and v.dept_id = #{bo.deptId}
            </if>
            <if test='bo.searchType == "2"'>
                and v.dept_id != #{bo.deptId}
            </if>
            <if test="bo.dbStatus != null and bo.dbStatus != ''">
                and l.db_status = #{bo.dbStatus}
            </if>
            <if test="bo.receiveStatus != null and bo.receiveStatus != ''">
                and l.receive_status = #{bo.receiveStatus}
            </if>
            <if test="bo.printStatus != null and bo.printStatus != ''">
                and l.print_status = #{bo.printStatus}
            </if>
            <if test="bo.linkType != '' and bo.linkType != null">
                and s.doc_class = #{bo.linkType}
            </if>
            <if test="bo.receiveUserName != '' and bo.receiveUserName != null">
                and l.receive_user_name = #{bo.receiveUserName}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                s.doc_class = #{bo.searchValue}
                OR s.doc_name LIKE CONCAT('%', #{bo.searchValue} ,'%')
                OR v.user_name LIKE CONCAT('%', #{bo.searchValue} ,'%')
                )
            </if>
        </where>
        ORDER BY l.receive_time DESC ,l.create_time desc
    </select>


    <select id="selectDistributeDept" parameterType="String" resultType="Long">
        SELECT ddl.dept_id
        FROM `doc_distribute_log` ddl
                 LEFT JOIN doc_version dv on ddl.doc_id = dv.doc_id
        where dv.status = '1'
          and ddl.db_status = '0'
          and dv.doc_id = #{docId}
    </select>

    <select id="queryPageListForSign" resultMap="DocDistributeLogResult"
            parameterType="com.rzdata.process.domain.bo.DocDistributeLogBo">
        SELECT l.*,
        v.start_date,
        v.end_date
        FROM doc_distribute_log l
        LEFT JOIN doc_standard s ON l.doc_id = s.id
        LEFT JOIN doc_version v ON l.version_id = v.id
        <!-- doc_distribute_log表内存的dept_id 是分发部门 不是文件的编制部门!! 所以需要先通过doc_id联表查询
             查出在doc_standard s 中该文件的编制部门 再用s去和部门表sys_dept连表查出该部门名称 放在DocDistributeLogResult的deptName字段给前端
             -->
        LEFT JOIN sys_dept d ON d.dept_id = v.dept_id
        <where>
            <!---->
            <!--前端传入的receiveStatus = 0表示查询未签收 1=已签收 -->
            <if test="bo.receiveStatus!=null and bo.receiveStatus!=''">
                l.receive_status = #{bo.receiveStatus}
            </if>
            <!--文件名称模糊查询-->
            <if test="bo.docName!='' and bo.docName!=null">
                <!--AND l.doc_name LIKE CONCAT('%', #{bo.doc_name} ,'%')-->
                AND l.doc_name like CONCAT('%', #{bo.docName} ,'%')
            </if>
            <!--文件类型查询条件-->
            <if test="bo.docClass!='' and bo.docClass!=null">
                AND l.doc_class = #{bo.docClass}
            </if>
            <!--编制部门查询条件-->
            <if test="bo.compileDeptId!='' and bo.compileDeptId!=null">
                AND l.compile_dept_id = #{bo.compileDeptId}
            </if>
            <!--生效日期查询条件-->
            <if test="bo.startTime!=null and bo.startTime!='' and bo.endTime!=null and bo.endTime!=''">
                AND v.start_date BETWEEN #{bo.startTime} AND #{bo.endTime}
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and l.distribute_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and l.distribute_time &lt; #{bo.params.endTime}
            </if>
            <!--分发时间查询条件-->
            <if test="bo.disStartTime!=null and bo.disStartTime!='' and bo.disEndTime!=null and bo.disEndTime!=''">
                AND l.distribute_time BETWEEN #{bo.disStartTime} AND #{bo.disEndTime}
            </if>
            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                AND ( ${bo.params.dataScope} )
            </if>
            ORDER BY l.distribute_time DESC
        </where>
    </select>

    <select id="getDocDistributeNumsByDocId" resultMap="DocDistributeLogResult">
        SELECT l.* , sum(l.nums) AS total_nums from doc_distribute_log l
        <where>
            <if test="docId!=null and docId!=''">
                l.doc_id = #{docId}
            </if>
            <if test="versionId!=null and versionId!=''">
                and l.version_id = #{versionId}
            </if>
            GROUP BY dept_id
        </where>
    </select>

    <select id="queryPageByThisDept" resultType="com.rzdata.process.domain.vo.DocDistributeLogVo">
        SELECT l.*,
        v.start_date,
        v.end_date,
        v.file_id,
        v.merge_file_id,
        v.encrypt_file_id,
        v.review_time,
        v.forever,
        w.proc_inst_id
        FROM doc_distribute_log l
        LEFT JOIN doc_version v ON l.version_id = v.id
        LEFT JOIN doc_workflow_apply_log w ON l.apply_id = w.id

        <where>
            <!-- 分发部门(dept_id)等于编制部门(complie_dept_id)并且分发部门(dept_id)等于当前登录用户所属部门(loginDeptId)-->
            l.dept_id = l.compile_dept_id AND l.dept_id= #{loginDeptId} AND v.status = 1
            <if test="bo.docClass!=null and bo.docClass!=''">
                AND l.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName!=null and bo.docName!=''">
                AND l.doc_name LIKE CONCAT('%', #{bo.docName} ,'%')
            </if>
            <if test="bo.docId!=null and bo.docId!=''">
                AND l.doc_id LIKE CONCAT('%', #{bo.docId} ,'%')
            </if>
            <!--分发时间查询条件-->
            <if test="bo.startTime!=null and bo.startTime!='' and bo.endTime!=null and bo.endTime!=''">
                AND v.update_time BETWEEN #{bo.startTime} AND #{bo.endTime}
            </if>
            group by l.doc_id,l.version_id
            order by v.start_date desc

        </where>
    </select>

    <select id="queryPageByOtherDept" resultType="com.rzdata.process.domain.vo.DocDistributeLogVo">
        SELECT l.*,
        v.start_date,
        v.end_date,
        v.file_id,
        v.merge_file_id,
        v.encrypt_file_id,
        v.review_time,
        w.proc_inst_id
        FROM doc_distribute_log l
        LEFT JOIN doc_version v ON l.version_id = v.id AND v.status = 1
        LEFT JOIN doc_workflow_apply_log w ON l.apply_id = w.id
        <where>
            <!-- 分发部门(dept_id)等于编制部门(complie_dept_id)并且分发部门(dept_id)等于当前登录用户所属部门(loginDeptId)-->
            l.dept_id != l.compile_dept_id AND l.dept_id= #{loginDeptId} AND v.status = 1
            <if test="bo.docClass!=null and bo.docClass!=''">
                AND l.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName!=null and bo.docName!=''">
                AND l.doc_name LIKE CONCAT('%', #{bo.docName} ,'%')
            </if>
            <if test="bo.docId!=null and bo.docId!=''">
                AND l.doc_id LIKE CONCAT('%', #{bo.docId} ,'%')
            </if>
            <if test="bo.deptId!=null and bo.deptId!=''">
                AND l.compile_dept_id =#{bo.deptId}
            </if>
            <!--分发时间查询条件-->
            <if test="bo.startTime!=null and bo.startTime!='' and bo.endTime!=null and bo.endTime!=''">
                AND v.update_time BETWEEN #{bo.startTime} AND #{bo.endTime}
            </if>
            group by l.doc_id,l.version_id
            order by v.start_date desc

        </where>
    </select>
</mapper>
