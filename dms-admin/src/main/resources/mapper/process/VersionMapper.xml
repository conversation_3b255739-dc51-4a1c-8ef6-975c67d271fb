<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.VersionMapper">

    <resultMap type="com.rzdata.process.domain.vo.VersionVo" id="VersionResult">
        <result property="indexNum" column="index_num"/>
        <result property="id" column="id"/>
        <result property="dataType" column="data_type"/>
        <result property="docId" column="doc_id"/>
        <result property="recordDocId" column="record_doc_id"/>
        <result property="applyId" column="apply_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="status" column="status"/>
        <result property="reason" column="reason"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="fileId" column="file_id"/>
        <result property="encryptFileId" column="encrypt_file_id"/>
        <result property="mergeFileId" column="merge_file_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="reviewTime" column="review_time"/>
        <result property="trainDept" column="train_dept"/>
        <result property="className" column="class_name"/>
        <result property="changeReason" column="change_reason"/>
        <result property="changeContent" column="change_content"/>
        <result property="changeFactor" column="change_factor"/>
        <result property="receiveStatus" column="receive_status"/>
        <result property="procInstId" column="proc_inst_id"/>
        <result property="productLine" column="product_line"/>
        <result property="process" column="process"/>
        <result property="productType" column="product_type"/>
        <result property="securityLevel" column="security_level"/>
        <result property="haveLinkFile" column="have_link_file"/>
        <result property="partNumber" column="part_number"/>
        <result property="partRemark" column="part_remark"/>
        <result property="factorys" column="factorys"/>
        <result property="custodyDeptId" column="custody_dept_id"/>
        <result property="shelfLife" column="shelf_life"/>
        <result property="custodyDeptName" column="custody_dept_name"/>
        <result property="customerCode" column="customer_code"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="productVersion" column="product_version"/>
        <result property="projectCode" column="project_code"/>
        <result column="file_purpose" property="filePurpose"/>
        <result column="regulation_standard_status" property="regulationStandardStatus"/>
        <result column="regulation_publish_date" property="regulationPublishDate"/>
        <result column="regulation_implement_date" property="regulationImplementDate"/>
        <result column="review_record_file_ids" property="reviewRecordFileIds"/>
        <result column="is_file_type" property="isFileType"/>
    </resultMap>

    <select id="selectVersionPage" resultMap="VersionResult">
        SELECT
        (@row_number := @row_number + 1) as index_num,
        v.*,
        v.id as version_id,
        s.doc_class,
        s.base_status,
        /*sd.dept_name,*/
        wk.proc_inst_id,
        dc.is_file_type
        /*u.nick_name*/
        FROM
        doc_version v
        LEFT JOIN doc_standard s ON v.standard_id = s.id
        LEFT JOIN doc_workflow_apply_log wk ON wk.id = v.apply_id
        LEFT JOIN basic_doc_class dc on s.doc_class=dc.id
        /*LEFT JOIN sys_dept sd on sd.dept_id = v.dept_id*/
        /*LEFT JOIN sys_user u ON v.user_name = u.user_name*/
        <where>
            s.status != '0'
            <if test="bo.isFileType != null and bo.isFileType != ''">
                and dc.is_file_type=#{bo.isFileType}
            </if>
            <if test="bo.expired">
                and date(v.shelf_life) &lt; now()
            </if>
<!--            公司台账中，工程样板和临时文件的过期文件则不展示，不过期的展示-->
            <if test="bo.type != null and bo.type != ''">
                and (dc.is_file_type not in ('Y', 'G') or (date(v.shelf_life) >= date(now())))
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and v.doc_id like CONCAT('%', #{bo.docId}, '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and v.status = #{bo.status}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and s.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docClassList != null and bo.docClassList.size > 0">
                and s.doc_class in
                <foreach collection="bo.docClassList" item="docClass" index="index" open="(" separator="," close=")">
                    #{docClass}
                </foreach>
            </if>
            <if test="bo.deptId != null and bo.deptId != ''">
                and (v.dept_id = #{bo.deptId} or find_in_set(v.dept_id,(select ancestors from sys_dept where dept_id = #{bo.deptId})))
            </if>
            <if test="bo.secDeptId != null and bo.secDeptId != ''">
                and (v.dept_id = #{bo.secDeptId} or
                find_in_set(v.dept_id,(select ancestors from sys_dept where dept_id = #{bo.secDeptId})))
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and v.doc_name like CONCAT('%', #{bo.docName}, '%')
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">
                and v.user_name in (select t1.user_name from sys_user t1 where t1.nick_name like CONCAT('%', #{bo.nickName}, '%') and t1.status = '0')
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and v.release_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and v.release_time &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.params.projectId != null and bo.params.projectId != ''">
                and s.project_id = #{bo.params.projectId}
            </if>
            <if test="bo.dataType != null and bo.dataType != ''">
                and s.data_type = #{bo.dataType}
            </if>
            <if test="bo.classType != null and bo.classType != ''">
                and v.class_type = #{bo.classType}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (v.doc_name like CONCAT('%', #{bo.searchValue}, '%')
                or  v.doc_id  like CONCAT('%', #{bo.searchValue}, '%'))
            </if>
            <if test="bo.partNumber!=null and bo.partNumber!=''">
                AND v.part_number like CONCAT('%', #{bo.partNumber}, '%')
            </if>
            <if test="bo.partRemark!=null and bo.partRemark!=''">
                AND v.part_remark like CONCAT('%', #{bo.partRemark}, '%')
            </if>
            <if test="bo.factorys!=null and bo.factorys!=''">
                AND v.factorys like CONCAT('%', #{bo.factorys}, '%')
            </if>

            <if test="bo.customerCode!=null and bo.customerCode!=''">
                AND v.customer_code like CONCAT('%', #{bo.customerCode}, '%')
            </if>
            <if test="bo.deviceCode!=null and bo.deviceCode!=''">
                AND v.device_code like CONCAT('%', #{bo.deviceCode}, '%')
            </if>
            <if test="bo.productVersion!=null and bo.productVersion!=''">
                AND v.product_version like CONCAT('%', #{bo.productVersion}, '%')
            </if>
            <if test="bo.projectCode!=null and bo.projectCode!=''">
                AND v.project_code like CONCAT('%', #{bo.projectCode}, '%')
            </if>
        </where>
        GROUP BY v.id
        ORDER BY v.start_date DESC
    </select>

    <select id="queryPageOtherDeptList" resultMap="VersionResult">
        SELECT
        v.*,
        v.id as version_id,
        s.doc_class,
        u.nick_name,
        s.base_status,
        d.dept_name,
        wk.proc_inst_id
        FROM
        doc_version v
        LEFT JOIN doc_standard s ON v.standard_id = s.id
        LEFT JOIN sys_dept d ON v.dept_id = d.dept_id
        LEFT JOIN sys_user u ON v.user_name = u.user_name
        LEFT JOIN doc_workflow_apply_log wk ON wk.id = v.apply_id
        right JOIN doc_distribute dd ON dd.version_id = v.id
        <where>
            s.status = '1'
            and (d.dept_id != #{bo.deptId} and !find_in_set(d.dept_id,(select ancestors from sys_dept where dept_id = #{bo.deptId})))
            and (
            (v.distribute_type LIKE '%dept%' and dd.type = 'dept' and (dd.receive_user_dept_id = #{bo.deptId} or find_in_set(dd.receive_user_dept_id,(select ancestors from sys_dept where dept_id = #{bo.deptId}))))
            or
            (v.distribute_type LIKE '%person%' and dd.type = 'person' and dd.receive_user_name = #{bo.userName})
            or
            (v.distribute_type = 'company' and dd.type = 'company' and dd.receive_user_dept_id in (select tenant_id from sys_user_tenant sut where sut.user_name = #{bo.userName}))
            )
            <if test="bo.docId != null and bo.docId != ''">
                and v.doc_id like CONCAT('%', #{bo.docId}, '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and v.status = #{bo.status}
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">
                and u.nick_name like CONCAT('%', #{bo.nickName}, '%')
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and s.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docClassList != null and bo.docClassList.size > 0">
                and s.doc_class in
                <foreach collection="bo.docClassList" item="docClass" index="index" open="(" separator="," close=")">
                    #{docClass}
                </foreach>
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and v.doc_name like CONCAT('%', #{bo.docName}, '%')
            </if>
            <if test="bo.outside != null and bo.outside != ''">
                and (v.dept_id = #{bo.outside} or find_in_set(#{bo.outside},d.ancestors))
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and v.release_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and v.release_time &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.params.projectId != null and bo.params.projectId != ''">
                and s.project_id = #{bo.params.projectId}
            </if>
            <if test="bo.dataType != null and bo.dataType != ''">
                and s.data_type = #{bo.dataType}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (v.doc_name like CONCAT('%', #{bo.searchValue}, '%')
                   or  v.doc_id  like CONCAT('%', #{bo.searchValue}, '%'))
            </if>
            <if test="bo.partNumber!=null and bo.partNumber!=''">
                AND v.part_number like CONCAT('%', #{bo.partNumber}, '%')
            </if>
            <if test="bo.partRemark!=null and bo.partRemark!=''">
                AND v.part_remark like CONCAT('%', #{bo.partRemark}, '%')
            </if>
            <if test="bo.factorys!=null and bo.factorys!=''">
                AND v.factorys like CONCAT('%', #{bo.factorys}, '%')
            </if>
            <if test="bo.customerCode!=null and bo.customerCode!=''">
                AND v.customer_code like CONCAT('%', #{bo.customerCode}, '%')
            </if>
            <if test="bo.deviceCode!=null and bo.deviceCode!=''">
                AND v.device_code like CONCAT('%', #{bo.deviceCode}, '%')
            </if>
            <if test="bo.productVersion!=null and bo.productVersion!=''">
                AND v.product_version like CONCAT('%', #{bo.productVersion}, '%')
            </if>
            <if test="bo.projectCode!=null and bo.projectCode!=''">
                AND v.project_code like CONCAT('%', #{bo.projectCode}, '%')
            </if>
        </where>
        GROUP BY v.id
        ORDER BY v.start_date DESC
    </select>

    <select id="selectDetailById" resultMap="VersionResult">
        SELECT v.*,
               s.doc_class,
               s.file_id,
               s.encrypt_file_id,
               d.dept_name,
               dc.class_name
        FROM doc_version v
                 LEFT JOIN doc_standard s ON v.standard_id = s.id
                 LEFT JOIN sys_dept d ON v.dept_id = d.dept_id
                 LEFT JOIN basic_doc_class dc ON s.doc_class = dc.id
        where v.id = #{id}
    </select>

    <select id="selectVersionListByAppId" resultMap="VersionResult">
        SELECT v.*,
               m.change_reason AS changeReason,
               m.content       AS changeContent,
               m.change_factor AS changeFactor
        FROM doc_version v
                 LEFT JOIN doc_modify_apply m ON v.id = m.version_id
        WHERE v.apply_id = #{applyId}
    </select>

    <select id="selectVersionList" resultMap="VersionResult">
        SELECT
        v.*,
        m.change_reason AS changeReason,
        m.content AS changeContent,
        m.change_factor AS changeFactor
        FROM
        doc_version v
        LEFT JOIN doc_modify_apply m ON v.id = m.version_id
        <where>
            v.apply_id in
            <foreach collection="ids" item="id" open="(" index="index" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="selectVersionByDocId" resultMap="VersionResult">
        select *
        from doc_version
        where STATUS = 1
          and doc_id = #{docId} LIMIT 1
    </select>

    <select id="selectVersionListByDocIdAndVersionId" resultMap="VersionResult">
        select *
        from doc_version
        where STATUS = 1
          and doc_id = #{docId}
          and id = #{versionId}
    </select>

    <select id="selectRecordFileCompany" resultType="VersionVo">
        SELECT
        IF( dll.link_type = 'DOC', dv.doc_name, dll.file_name ) AS doc_name,
        IF( dll.link_type = 'DOC', dv.version_value, dll.version_value ) AS version_value,
        IF( dll.link_type = 'DOC', ds.doc_class, dll.doc_class ) AS doc_class,
        dll.file_id,
        dll.link_code as record_doc_id,
        dv.review_time,
        dv.status,
        dll.start_date,
        dll.end_date,
        dv.train_dept,
        dv.dept_id,
        IF( dll.link_type = 'RECORD', dll.link_code, dv.doc_id ) AS doc_id,
        dv.id,
        dv.id as version_id,
        dv.user_name,
        dll.release_time,
        w.proc_inst_id,
        dv.apply_id,
        dv.distribute_type
        FROM
        doc_link_log dll
        LEFT JOIN doc_version_link dvl ON dll.id = dvl.link_id
        LEFT JOIN doc_version dv ON dvl.version_id = dv.id
        LEFT JOIN doc_standard ds ON dv.standard_id = ds.id
        LEFT JOIN doc_workflow_apply_log w ON dv.apply_id = w.id
        <where>
            and ds.status = '1'
            AND dll.link_type = 'RECORD'
            <if test="bo.docClass != null and bo.docClass != ''">
                and (ds.doc_class = #{bo.docClass} or dll.doc_class = #{bo.docClass})
            </if>
            <if test="bo.params.projectId != null and bo.params.projectId != ''">
                and ds.project_id = #{bo.params.projectId}
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and (dv.doc_id = #{bo.docId} or dll.link_code = #{bo.docId})
            </if>
            <if test="bo.status != null and bo.status != ''">
                and dv.status = #{bo.status}  AND dll.`status` =  #{bo.status}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and (dv.doc_name like concat('%', #{bo.docName}, '%') or dll.file_name like concat('%', #{bo.docName},
                '%'))
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and dv.start_date >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and dv.start_date &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.deptId != null and bo.deptId != ''">
                and dv.dept_id = #{bo.deptId}
            </if>
        </where>
        ORDER BY dv.start_date DESC
    </select>

    <select id="selectRecordFileCompanyOtherDept" resultType="VersionVo">
        SELECT
        IF( dll.link_type = 'DOC', dv.doc_name, dll.file_name ) AS doc_name,
        IF( dll.link_type = 'DOC', dv.version_value, dll.version_value ) AS version_value,
        IF( dll.link_type = 'DOC', ds.doc_class, dll.doc_class ) AS doc_class,
        dll.file_id,
        dll.link_code as record_doc_id,
        dv.review_time,
        dv.status,
        dll.start_date,
        dll.end_date,
        dv.train_dept,
        dv.dept_id,
        IF( dll.link_type = 'RECORD', dll.link_code, dv.doc_id ) AS doc_id,
        dv.id,
        dv.id as version_id,
        dv.user_name,
        dll.release_time,
        w.proc_inst_id,
        dv.apply_id
        FROM
        doc_link_log dll
        LEFT JOIN doc_version_link dvl ON dll.id = dvl.link_id
        LEFT JOIN doc_version dv ON dvl.version_id = dv.id
        LEFT JOIN doc_standard ds ON dv.standard_id = ds.id
        LEFT JOIN doc_workflow_apply_log w ON dv.apply_id = w.id
        right JOIN doc_distribute dd ON dd.version_id = dv.id
        <where>
            and ds.status = '1'
            AND dll.link_type = 'RECORD'
            and dv.dept_id != #{bo.deptId}
            and (
            (dv.distribute_type LIKE '%dept%' and dd.type = 'dept' and dd.receive_user_dept_id = #{bo.deptId})
            or
            (dv.distribute_type LIKE '%person%' and dd.type = 'person' and dd.receive_user_name = #{bo.userName}))
            <if test="bo.docClass != null and bo.docClass != ''">
                and (ds.doc_class = #{bo.docClass} or dll.doc_class = #{bo.docClass})
            </if>
            <if test="bo.docClassList != null and bo.docClassList.size > 0">
                and s.doc_class in
                <foreach collection="bo.docClassList" item="docClass" index="index" open="(" separator="," close=")">
                    #{docClass}
                </foreach>
            </if>
            <if test="bo.params.projectId != null and bo.params.projectId != ''">
                and ds.project_id = #{bo.params.projectId}
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and (dv.doc_id = #{bo.docId} or dll.link_code = #{bo.docId})
            </if>
            <if test="bo.outside != null and bo.outside != ''">
                and dv.dept_id = #{bo.outside}
            </if>
            <if test="bo.status != null and bo.status != ''">
                and dv.status = #{bo.status}  AND dll.`status` =  #{bo.status}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and (dv.doc_name like concat('%', #{bo.docName}, '%') or dll.file_name like concat('%', #{bo.docName},
                '%'))
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and dv.start_date >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and dv.start_date &lt;= #{bo.params.endTime}
            </if>
        </where>
        ORDER BY dv.start_date DESC
    </select>

    <select id="selectRecordFile" resultType="VersionVo">
        SELECT
        IF( dll.link_type = 'DOC', dv.doc_name, dll.file_name ) AS doc_name,
        IF( dll.link_type = 'DOC', dv.version_value, dll.version_value ) AS version_value,
        IF( dll.link_type = 'DOC', ds.doc_class, dll.doc_class ) AS doc_class,
        IF( dll.link_type = 'DOC', dv.merge_file_id, dll.file_id ) AS merge_file_id,
        IF( dll.link_type = 'DOC', dv.encrypt_file_id, dll.file_id ) AS encrypt_file_id,
        dll.file_id,
        dll.link_code as record_doc_id,
        dv.review_time,
        dv.start_date,
        dv.end_date,
        dv.status,
        dv.train_dept,
        dv.dept_id,
        dv.doc_id,
        dv.id,
        dv.id as version_id,
        ddl.receive_status,
        w.proc_inst_id,
        ddl.apply_id
        FROM
        doc_link_log dll
        LEFT JOIN doc_version_link dvl ON dll.id = dvl.link_id
        LEFT JOIN doc_version dv ON dvl.version_id = dv.id
        LEFT JOIN `doc_standard` ds ON dv.standard_id = ds.id
        LEFT JOIN `doc_distribute_log` ddl ON ddl.version_id = dv.id
        LEFT JOIN doc_workflow_apply_log w ON ddl.apply_id = w.id
        <where>
            ds.`status` = '1'
            AND dll.link_type IN ( 'RECORD', 'DOC' )
            AND dll.`status` = '1'
            <if test="bo.docClass != null and bo.docClass != ''">
                and (ds.doc_class = #{bo.docClass} or dll.doc_class = #{bo.docClass})
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and (dv.doc_id = #{bo.docId} or dll.link_code = #{bo.docId})
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and (dv.doc_name like concat('%', #{bo.docName}, '%') or dll.file_name like concat('%', #{bo.docName},
                '%'))
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and dv.start_date >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and dv.start_date &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.deptId != null and bo.deptId != ''">
                and dv.dept_id = #{bo.deptId}
            </if>
            <if test="bo.inside != null and bo.inside != ''">
                and ddl.dept_id = #{bo.inside}
                and dv.dept_id = #{bo.inside}
            </if>
            <if test="bo.outside != null and bo.outside != ''">
                and ddl.dept_id = #{bo.outside}
                and dv.dept_id != #{bo.outside}
                AND dv.id IN (SELECT version_id FROM doc_distribute_item where dept_id = #{bo.outside})
            </if>
        </where>
        ORDER BY dv.start_date DESC
    </select>

    <select id="selectDeptFile" resultType="VersionVo">
        SELECT
        dv.doc_name,
        dv.doc_id,
        dv.version_value,
        dv.dept_id,
        ds.doc_class,
        dv.start_date,
        dv.end_date,
        dv.id
        FROM
        doc_version dv
        LEFT JOIN doc_standard ds ON ds.id = dv.standard_id
        WHERE
        ds.`status` = '1'
        AND dv.`status` = '1'
        <if test="bo.docId != null and bo.docId != ''">
            and dv.doc_id = #{bo.docId}
        </if>
        <if test="bo.docName != null and bo.docName != ''">
            and dv.doc_name like concat('%', #{bo.docName}, '%')
        </if>
        <if test="bo.docClass != null and bo.docClass != ''">
            AND ds.doc_class = #{bo.docClass}
        </if>
        <if test="bo.params.startTime != null and bo.params.startTime != ''">
            and dv.start_date >= #{bo.params.startTime}
        </if>
        <if test="bo.params.endTime != null and bo.params.endTime != ''">
            and dv.start_date &lt;= #{bo.params.endTime}
        </if>
        <if test="bo.deptId != null and bo.deptId != ''">
            and dv.dept_id = #{bo.deptId}
        </if>
        <if test="bo.inside != null and bo.inside != ''">
            and dv.dept_id = #{bo.inside}
        </if>
        <if test="bo.outside != null and bo.outside != ''">
            and dv.dept_id != #{bo.outside}
            AND dv.id IN (SELECT version_id FROM doc_distribute_item where dept_id = #{bo.outside})
        </if>
    </select>

    <select id="checkAuthByBorrow" resultType="java.lang.Integer">
            SELECT COUNT(*)
            FROM doc_borrow_apply dba
            JOIN doc_borrow_apply_user dbau ON dba.id = dbau.apply_id
            <where>
                <!-- 查询借阅流程为结束且通过的-->
                dba.status = 'done'
                AND dba.apply_status = 'pass'
                <if test="docId!=null and docId!=''">
                    AND dbau.doc_id = #{docId}
                </if>
                <if test="versionId!=null and versionId!=''">
                    AND dbau.version_id = #{versionId}
                </if>
                <if test="date!=null">
                    AND #{date} BETWEEN dba.start_time AND dba.end_time
                </if>
            </where>
    </select>

    <select id="checkAuthByDis" resultType="java.lang.Integer">
        select count(*) from doc_version dv
        RIGHT JOIN doc_distribute dd on dv.id = dd.version_id
        where
        ((dv.distribute_type LIKE '%dept%' and dd.type = 'dept' and (dd.receive_user_dept_id = #{deptId} or find_in_set(dd.receive_user_dept_id,(select ancestors from sys_dept where dept_id = #{deptId}))))
        or
        (dv.distribute_type LIKE '%person%' and dd.type = 'person' and dd.receive_user_name = #{userName})
        or
         (dv.distribute_type = 'company' and dd.type = 'company' and dd.receive_user_dept_id in (select tenant_id from sys_user_tenant sut where sut.user_name = #{userName}))
        )
        and dd.version_id = #{versionId}
    </select>


    <!--    查询版本和标准文件的集合-->
    <select id="versionAndStandardList" resultType="com.rzdata.process.domain.Version">
        SELECT
        v.*
        FROM doc_version v
        JOIN doc_standard m ON v.standard_id = m.id
        where 1=1
        <if test="status!=null and status!='' and status!='null'">
            AND v.status = #{status}
        </if>
        and v.standard_id in
            <foreach collection="ids" item="id" open="(" index="index" separator="," close=")">
                #{id}
            </foreach>
    </select>

    <select id="selectRecordVersionList" resultType="com.rzdata.process.domain.Version">
        select dv.* from doc_version_link dvl
                             LEFT JOIN doc_link_log dll on dvl.link_id = dll.id
                             LEFT JOIN doc_version dv on dv.doc_id = dll.link_code
        where dvl.version_id = #{versionId} and dll.link_type= 'RECORD' and dv.`status` = '1'
    </select>



    <!--查询版本中，文件分类+物料编码+物料描述是否唯一-->
    <select id="queryVersionPartList" resultType="com.rzdata.process.domain.vo.VersionVo">
        SELECT
            v.*,
            m.doc_class
        FROM doc_version v
        JOIN doc_standard m ON v.standard_id = m.id
        <where>
            AND v.status = '1'
            <if test="bo.partNumber!=null and bo.partNumber!=''">
                AND v.part_number = #{bo.partNumber}
            </if>
            <if test="bo.partRemark!=null and bo.partRemark!=''">
                AND v.part_remark = #{bo.partRemark}
            </if>
            <if test="bo.docClass!=null and bo.docClass!=''">
                AND m.doc_class = #{bo.docClass}
            </if>
            <if test="bo.neVersionId!=null and bo.neVersionId!=''">
                AND v.id != #{bo.neVersionId}
            </if>
            <if test="bo.partNumberIsNotNull !=null and bo.partNumberIsNotNull!=''">
                AND v.part_number is not null AND v.part_remark is not null
                or (v.status = '1' and v.device_code is not null AND v.device_name is not null )
            </if>
            <if test="bo.deviceCode!=null and bo.deviceCode!=''">
                AND v.device_code = #{bo.deviceCode}
            </if>
            <if test="bo.deviceName!=null and bo.deviceName!=''">
                AND v.device_name = #{bo.deviceName}
            </if>
        </where>
    </select>

    <select id="pageApi" resultType="com.rzdata.process.domain.dto.VersionDTO">
         select dv.id,dv.doc_name,dv.doc_id,dv.version_value,dv.customer_code,dv.part_number,dv.part_remark,dv.factorys,dv.product_version,dv.dept_id,dv.user_name,
               su.nick_name,sd.dept_name,
               bdc.class_name,ds.doc_class,dv.start_date,dv.`status`
         from doc_version dv
         LEFT JOIN doc_standard ds on dv.standard_id=ds.id
         LEFT JOIN basic_doc_class bdc on ds.doc_class=bdc.id
         LEFT JOIN sys_user su on dv.user_name = su.user_name
         LEFT JOIN sys_dept sd on dv.dept_id = sd.dept_id
        <where>
            ds.`status` = '1' and dv.`status` = '1' and dv.data_type = 'stdd'
            <if test="limitDocClass!=null and limitDocClass!=''">
                and FIND_IN_SET(#{limitDocClass},bdc.ancestors)
            </if>
            <if test="bo.docClass!=null and bo.docClass!=''">
                and FIND_IN_SET(#{bo.docClass},bdc.ancestors)
            </if>
            <if test="bo.partNumber!=null and bo.partNumber!=''">
                and FIND_IN_SET(#{bo.partNumber},dv.part_number)
            </if>
            <if test="bo.productVersion!=null and bo.productVersion!=''">
                and dv.product_version = #{bo.productVersion}
            </if>
            <if test="bo.className!=null and bo.className!=''">
                and bdc.class_name = #{bo.className}
            </if>
            <if test="bo.docName!=null and bo.docName!=''">
                and dv.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.docId!=null and bo.docId!=''">
                and dv.doc_id = #{bo.docId}
            </if>
            <if test="bo.versionValue!=null and bo.versionValue!=''">
                and dv.version_value = #{bo.versionValue}
            </if>
        </where>
    </select>
</mapper>
