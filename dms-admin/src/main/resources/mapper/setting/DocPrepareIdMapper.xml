<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocPrepareIdMapper">

    <resultMap type="com.rzdata.setting.domain.DocPrepareId" id="DocPrepareIdResult">
        <result property="docId" column="doc_id"/>
        <result property="codeType" column="code_type"/>
        <result property="docClass" column="doc_class"/>
        <result property="remark" column="remark"/>
        <result property="parentDocId" column="parent_doc_id"/>
        <result property="applyBy" column="apply_by"/>
        <result property="applyTime" column="apply_time"/>
        <result property="useStatus" column="use_status"/>
        <result property="useTime" column="use_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="validatePrepareId" parameterType="com.rzdata.setting.domain.bo.DocPrepareIdBo" resultType="com.rzdata.setting.domain.vo.DocPrepareIdVo">
        SELECT dpi.doc_id,dpi.use_status,bcrl.business_id FROM `doc_prepare_id` dpi
        LEFT JOIN basic_code_rule_log bcrl on dpi.doc_id = bcrl.rule_value
        where dpi.doc_id = #{bo.docId} and dpi.use_status = '1'
    </select>
</mapper>
