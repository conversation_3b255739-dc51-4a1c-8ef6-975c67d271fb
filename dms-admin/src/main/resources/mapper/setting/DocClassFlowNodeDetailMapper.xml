<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocClassFlowNodeDetailMapper">

    <resultMap type="com.rzdata.setting.domain.DocClassFlowNodeDetail" id="DocClassFlowNodeDetailResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="nodeId" column="node_id"/>
        <result property="type" column="type"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="funName" column="fun_name"/>
        <result property="funCondition" column="fun_condition"/>
        <result property="sort" column="sort"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <!-- 根据流程KEY和流程环节，获取环节权限明细配置 -->
    <select id="queryList" parameterType="java.util.HashMap" resultMap="DocClassFlowNodeDetailResult" >
        select t.* from basic_doc_class_flow_node_detail t
            join basic_doc_class_flow_node n on t.node_id = n.id
            join basic_doc_class_flow f on f.id = n.flow_id
        where f.id = #{flowId} and n.node_code = #{nodeCode} and n.delete_flag = 'N'
        order by t.sort asc
    </select>

</mapper>
