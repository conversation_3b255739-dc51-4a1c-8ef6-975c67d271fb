<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.CodeRuleMapper">

    <resultMap type="com.rzdata.setting.domain.CodeRule" id="CodeRuleResult">
        <result property="id" column="id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="resetCycle" column="reset_cycle"/>
        <result property="numberInitValue" column="number_init_value"/>
        <result property="numberDigit" column="number_digit"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectDeptCodeByName" resultType="string">
        SELECT
            d.dept_code
        FROM
            sys_user u
                INNER JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE
            u.user_name = #{name}
    </select>

    <select id="selectEffectDate" resultType="date">
        select IFNULL(start_date,NOW()) from doc_version where id = #{versionId}
    </select>
</mapper>
