<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocClassFlowNodeMapper">

    <resultMap type="com.rzdata.setting.domain.DocClassFlowNode" id="DocClassFlowNodeResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="flowId" column="flow_id"/>
        <result property="nodeCode" column="node_code"/>
        <result property="nodeName" column="node_name"/>
        <result property="pageMode" column="page_mode"/>
        <result property="appendixMode" column="appendix_mode"/>
        <result property="moduleConfig" column="module_config"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="getNodeList" resultType="DocClassFlowNodeVo">
        select dcfn.*,dcfnd.fun_condition from basic_doc_class_flow_node dcfn
        LEFT JOIN basic_doc_class_flow_node_detail dcfnd on dcfn.id = dcfnd.node_id
        <where>
            <if test="flowId != null and flowId != ''">
                and dcfn.flow_id = #{flowId}
            </if>
            <if test="code != null and code != ''">
                and dcfnd.`code` = #{code}
            </if>
        </where>
    </select>
</mapper>
