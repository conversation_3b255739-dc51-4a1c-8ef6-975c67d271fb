package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.ProjectPerson;
import com.rzdata.setting.domain.bo.ProjectPersonBo;
import com.rzdata.setting.domain.vo.ProjectPersonVo;
import com.rzdata.setting.mapper.ProjectPersonMapper;
import com.rzdata.setting.service.IProjectPersonService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.util.stream.Collectors;

/**
 * 项目人员配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class ProjectPersonServiceImpl extends ServicePlusImpl<ProjectPersonMapper, ProjectPerson, ProjectPersonVo> implements IProjectPersonService {

    @Override
    public ProjectPersonVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ProjectPersonVo> queryPageList(ProjectPersonBo bo) {
        PagePlus<ProjectPerson, ProjectPersonVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ProjectPersonVo> queryList(ProjectPersonBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ProjectPerson> buildQueryWrapper(ProjectPersonBo bo) {
        LambdaQueryWrapper<ProjectPerson> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProjectCode()), ProjectPerson::getProjectCode, bo.getProjectCode());
        lqw.like(StringUtils.isNotBlank(bo.getUsers()), ProjectPerson::getUsers, bo.getUsers());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ProjectPersonBo bo) throws Exception {
        ProjectPerson add = BeanUtil.toBean(bo, ProjectPerson.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ProjectPersonBo bo) throws Exception {
        ProjectPerson update = BeanUtil.toBean(bo, ProjectPerson.class);
        validEntityBeforeSave(update);
        update.setUpdateTime(new Date());
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ProjectPerson entity) throws Exception {
        // 校验项目编码是否已存在
        LambdaQueryWrapper<ProjectPerson> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProjectPerson::getProjectCode, entity.getProjectCode());
        if (entity.getId() != null) {
            lqw.ne(ProjectPerson::getId, entity.getId());
        }
        ProjectPerson existEntity = this.getOne(lqw);
        if (existEntity != null) {
            throw new Exception("项目编码已存在，请使用其他编码");
        }
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // 可以在这里添加删除前的校验逻辑
        }
        return removeByIds(ids);
    }

    @Override
    public List<JSONObject> getUsersByProjectCodes(List<String> projectCodes) {
        if (projectCodes == null || projectCodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用IN查询批量获取
        LambdaQueryWrapper<ProjectPerson> lqw = Wrappers.lambdaQuery();
        lqw.in(ProjectPerson::getProjectCode, projectCodes);
        lqw.select(ProjectPerson::getUsers); // 只查询users字段

        return this.list(lqw).stream()
                .map(ProjectPerson::getUsers)
                .filter(StringUtils::isNotBlank)
                .map(JSON::parseArray)
                .flatMap(Collection::stream)
                .map(obj -> (JSONObject) obj)
                .collect(Collectors.toList());
    }


}
