package com.rzdata.setting.service;

import com.rzdata.setting.domain.ProjectPerson;
import com.rzdata.setting.domain.vo.ProjectPersonVo;
import com.rzdata.setting.domain.bo.ProjectPersonBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 项目人员配置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IProjectPersonService extends IServicePlus<ProjectPerson, ProjectPersonVo> {

    /**
     * 查询单个
     * @param id 主键ID
     * @return 项目人员配置视图对象
     */
    ProjectPersonVo queryById(String id);

    /**
     * 查询分页列表
     * @param bo 项目人员配置业务对象
     * @return 分页数据
     */
    TableDataInfo<ProjectPersonVo> queryPageList(ProjectPersonBo bo);

    /**
     * 查询列表
     * @param bo 项目人员配置业务对象
     * @return 项目人员配置视图对象列表
     */
    List<ProjectPersonVo> queryList(ProjectPersonBo bo);

    /**
     * 根据新增业务对象插入项目人员配置
     * @param bo 项目人员配置新增业务对象
     * @return 是否成功
     */
    Boolean insertByBo(ProjectPersonBo bo) throws Exception;

    /**
     * 根据编辑业务对象修改项目人员配置
     * @param bo 项目人员配置编辑业务对象
     * @return 是否成功
     */
    Boolean updateByBo(ProjectPersonBo bo) throws Exception;

    /**
     * 校验并删除数据
     * @param ids 主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
