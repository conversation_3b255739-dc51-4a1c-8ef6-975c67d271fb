package com.rzdata.setting.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.bo.ProjectPersonBo;
import com.rzdata.setting.domain.vo.ProjectPersonVo;
import com.rzdata.setting.service.IProjectPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 项目人员配置Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Validated
@Api(value = "项目人员配置控制器", tags = {"项目人员配置管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/projectPerson")
public class ProjectPersonController extends BaseController {

    private final IProjectPersonService iProjectPersonService;

    /**
     * 查询项目人员配置列表
     */
    @ApiOperation("查询项目人员配置列表")
    @GetMapping("/list")
    public TableDataInfo<ProjectPersonVo> list(@Validated(QueryGroup.class) ProjectPersonBo bo) {
        return iProjectPersonService.queryPageList(bo);
    }

    /**
     * 导出项目人员配置列表
     */
    @ApiOperation("导出项目人员配置列表")
    @Log(title = "项目人员配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ProjectPersonBo bo, HttpServletResponse response) {
        List<ProjectPersonVo> list = iProjectPersonService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目人员配置", ProjectPersonVo.class, response);
    }

    /**
     * 获取项目人员配置详细信息
     */
    @ApiOperation("获取项目人员配置详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ProjectPersonVo> getInfo(@ApiParam("主键")
                                               @NotNull(message = "主键不能为空")
                                               @PathVariable("id") String id) {
        return AjaxResult.success(iProjectPersonService.queryById(id));
    }

    /**
     * 新增项目人员配置
     */
    @ApiOperation("新增项目人员配置")
    @Log(title = "项目人员配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ProjectPersonBo bo) {
        try {
            Integer flag = iProjectPersonService.insertByBo(bo) ? 1 : 0;
            return toAjax(flag);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 修改项目人员配置
     */
    @ApiOperation("修改项目人员配置")
    @Log(title = "项目人员配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ProjectPersonBo bo) {
        try {
            Integer flag = iProjectPersonService.updateByBo(bo) ? 1 : 0;
            return toAjax(flag);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 删除项目人员配置
     */
    @ApiOperation("删除项目人员配置")
    @Log(title = "项目人员配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iProjectPersonService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 根据项目编码查询人员配置（支持多个项目编码，用逗号分隔）
     */
    @ApiOperation("根据项目编码查询人员配置")
    @PreAuthorize("@ss.hasPermi('setting:projectPerson:query')")
    @GetMapping("/getByProjectCode/{projectCode}")
    public AjaxResult<List<ProjectPersonVo>> getByProjectCode(@ApiParam("项目编码（支持多个，用逗号分隔）")
                                                              @NotBlank(message = "项目编码不能为空")
                                                              @PathVariable("projectCode") String projectCode) {
        // 按逗号分割项目编码
        String[] projectCodes = projectCode.split(",");
        List<ProjectPersonVo> resultList = new ArrayList<>();

        for (String code : projectCodes) {
            String trimmedCode = code.trim();
            if (!trimmedCode.isEmpty()) {
                ProjectPersonBo bo = new ProjectPersonBo();
                bo.setProjectCode(trimmedCode);
                List<ProjectPersonVo> list = iProjectPersonService.queryList(bo);
                resultList.addAll(list);
            }
        }

        return AjaxResult.success(resultList);
    }
}
